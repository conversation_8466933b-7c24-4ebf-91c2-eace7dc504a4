"""
Data models for the check fraud detection system.
"""
from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum


class FraudReason(Enum):
    """Enumeration of possible fraud reasons."""
    AMOUNT_MISMATCH = "amount_mismatch"
    SUSPICIOUS_SIGNATURE = "suspicious_signature"
    UNUSUAL_BEHAVIOR = "unusual_behavior"
    INVALID_DATE = "invalid_date"
    DUPLICATE_CHECK = "duplicate_check"
    EXCESSIVE_AMOUNT = "excessive_amount"
    FREQUENT_TRANSACTIONS = "frequent_transactions"
    UNKNOWN_PAYEE = "unknown_payee"


@dataclass
class CheckElements:
    """Extracted elements from a check."""
    payee: Optional[str] = None
    amount_words: Optional[str] = None
    amount_digits: Optional[float] = None
    date: Optional[str] = None
    account_number: Optional[str] = None
    check_number: Optional[str] = None
    signature_present: bool = False
    bank_name: Optional[str] = None
    raw_text: Optional[str] = None
    confidence_score: float = 0.0


@dataclass
class Transaction:
    """Transaction data model."""
    customer_id: str
    transaction_id: str
    amount: float
    payee: str
    date: datetime
    transaction_type: str
    account_number: str
    check_number: Optional[str] = None
    location: Optional[str] = None
    category: Optional[str] = None


@dataclass
class CustomerProfile:
    """Customer profile for behavioral analysis."""
    customer_id: str
    avg_transaction_amount: float
    max_transaction_amount: float
    common_payees: List[str]
    transaction_frequency: float  # transactions per day
    preferred_categories: List[str]
    account_age_days: int
    risk_score: float = 0.0


@dataclass
class FraudDetectionResult:
    """Result of fraud detection analysis."""
    is_fraud: bool
    confidence_score: float
    fraud_reasons: List[FraudReason]
    risk_score: float
    extracted_elements: CheckElements
    behavioral_analysis: Dict[str, Any]
    recommendations: List[str]

    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary for JSON serialization."""
        result_dict = {
            "is_fraud": self.is_fraud,
            "confidence_score": self.confidence_score,
            "fraud_reasons": [reason.value for reason in self.fraud_reasons],
            "risk_score": self.risk_score,
            "extracted_elements": {
                "payee": self.extracted_elements.payee,
                "amount_words": self.extracted_elements.amount_words,
                "amount_digits": self.extracted_elements.amount_digits,
                "date": self.extracted_elements.date,
                "account_number": self.extracted_elements.account_number,
                "check_number": self.extracted_elements.check_number,
                "signature_present": self.extracted_elements.signature_present,
                "bank_name": self.extracted_elements.bank_name,
                "confidence_score": self.extracted_elements.confidence_score
            },
            "behavioral_analysis": self.behavioral_analysis,
            "recommendations": self.recommendations
        }

        # Add signature analysis if available
        if hasattr(self, 'signature_analysis'):
            result_dict["signature_analysis"] = self.signature_analysis

        return result_dict
