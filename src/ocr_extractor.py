"""
OCR-based text extraction with multiple engine support.
"""
import cv2
import numpy as np
from PIL import Image
import logging
import re
import ollama
from typing import Optional, Dict, Any, List, Tuple
from .models import CheckElements

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OCRExtractor:
    """Extract text using OCR engines and parse with LLM."""

    def __init__(self, preferred_engine: str = "tesseract", llm_model: str = "gemma3:latest"):
        """
        Initialize OCR extractor.

        Args:
            preferred_engine: OCR engine to use ('tesseract', 'easyocr', 'paddleocr')
            llm_model: LLM model for text parsing
        """
        self.preferred_engine = preferred_engine
        self.llm_model = llm_model
        self.available_engines = self._check_available_engines()
        self.ollama_client = ollama.Client()

        logger.info(f"OCR Extractor initialized with engine: {preferred_engine}")
        logger.info(f"Available OCR engines: {self.available_engines}")

    def _check_available_engines(self) -> List[str]:
        """Check which OCR engines are available."""
        available = []

        # Check Tesseract
        try:
            import pytesseract
            available.append("tesseract")
        except ImportError:
            logger.warning("Tesseract not available")

        # Check EasyOCR
        try:
            import easyocr
            # Test if it can actually initialize
            _ = easyocr.Reader(['en'], gpu=False, verbose=False)
            available.append("easyocr")
        except Exception as e:
            logger.warning(f"EasyOCR not available: {e}")

        # Check PaddleOCR
        try:
            from paddleocr import PaddleOCR
            available.append("paddleocr")
        except ImportError:
            logger.warning("PaddleOCR not available")

        # Check Llama OCR (via Ollama)
        try:
            # Test if Ollama is available and has a vision model
            import ollama
            client = ollama.Client()
            models = client.list()
            vision_models = [m for m in models['models'] if 'llava' in m['name'].lower() or 'vision' in m['name'].lower()]
            if vision_models:
                available.append("llama_ocr")
            else:
                logger.warning("Llama OCR not available: No vision models found")
        except Exception as e:
            logger.warning(f"Llama OCR not available: {e}")

        return available

    def extract_text_tesseract(self, image_path: str) -> str:
        """Extract text using Tesseract OCR."""
        try:
            import pytesseract

            # Load and preprocess image
            img = cv2.imread(image_path)
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            # Apply preprocessing for better OCR
            # Denoise
            denoised = cv2.fastNlMeansDenoising(gray)

            # Enhance contrast
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(denoised)

            # Threshold
            _, thresh = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # Extract text with multiple configs for better handwriting recognition
            configs = [
                # Config 1: General handwriting (PSM 6 - single uniform block)
                r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,/$-: ',
                # Config 2: Handwriting optimized (PSM 8 - single word)
                r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,/$-: ',
                # Config 3: Mixed text (PSM 3 - fully automatic)
                r'--oem 3 --psm 3 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,/$-: '
            ]

            best_text = ""
            max_length = 0

            # Try multiple configurations and pick the one with most text
            for config in configs:
                try:
                    text = pytesseract.image_to_string(thresh, config=config)
                    if len(text.strip()) > max_length:
                        max_length = len(text.strip())
                        best_text = text
                except:
                    continue

            text = best_text if best_text else pytesseract.image_to_string(thresh)

            return text.strip()

        except Exception as e:
            logger.error(f"Tesseract OCR failed: {e}")
            return ""

    def extract_text_easyocr(self, image_path: str) -> str:
        """Extract text using EasyOCR."""
        try:
            import easyocr

            # Initialize reader (English, CPU only for compatibility)
            reader = easyocr.Reader(['en'], gpu=False, verbose=False)

            # Extract text
            results = reader.readtext(image_path)

            # Combine all detected text
            text_lines = []
            for (bbox, text, confidence) in results:
                if confidence > 0.5:  # Filter low confidence detections
                    text_lines.append(text)

            return '\n'.join(text_lines)

        except Exception as e:
            logger.error(f"EasyOCR failed: {e}")
            return ""

    def extract_text_paddleocr(self, image_path: str) -> str:
        """Extract text using PaddleOCR."""
        try:
            from paddleocr import PaddleOCR

            # Initialize PaddleOCR
            ocr = PaddleOCR(use_angle_cls=True, lang='en', show_log=False)

            # Extract text
            results = ocr.ocr(image_path, cls=True)

            # Combine all detected text
            text_lines = []
            for line in results[0]:
                if line[1][1] > 0.5:  # Filter low confidence detections
                    text_lines.append(line[1][0])

            return '\n'.join(text_lines)

        except Exception as e:
            logger.error(f"PaddleOCR failed: {e}")
            return ""

    def extract_text_llama_ocr(self, image_path: str) -> str:
        """Extract text using Llama OCR (Vision LLM optimized for OCR)."""
        try:
            import base64
            from PIL import Image
            import io

            # Encode image to base64
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # Resize if too large (for better processing)
                max_size = 1024
                if max(img.size) > max_size:
                    ratio = max_size / max(img.size)
                    new_size = tuple(int(dim * ratio) for dim in img.size)
                    img = img.resize(new_size, Image.Resampling.LANCZOS)

                # Convert to base64
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG', quality=95)
                image_b64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

            # Create OCR-specific prompt for handwritten text
            prompt = """
            You are an expert OCR system specialized in reading handwritten and printed text from check images.

            Please extract ALL visible text from this image with maximum accuracy. Focus on:

            1. HANDWRITTEN text (names, amounts, signatures)
            2. PRINTED text (bank names, routing numbers, account numbers)
            3. NUMBERS (amounts, dates, check numbers, account numbers)
            4. MICR line numbers at the bottom

            IMPORTANT INSTRUCTIONS:
            - Extract EVERY piece of text you can see, even if partially visible
            - For handwritten text, make your best interpretation
            - Include punctuation, symbols, and formatting
            - Preserve the spatial layout when possible
            - If text is unclear, provide your best guess in [brackets]
            - Pay special attention to the MICR line (bottom numbers)

            Please provide the extracted text exactly as you see it, line by line:
            """

            # Call Llama vision model
            response = self.ollama_client.chat(
                model='llava:latest',  # Use the available vision model
                messages=[
                    {
                        'role': 'user',
                        'content': prompt,
                        'images': [image_b64]
                    }
                ]
            )

            extracted_text = response['message']['content']
            logger.info(f"Llama OCR extracted {len(extracted_text)} characters")
            return extracted_text.strip()

        except Exception as e:
            logger.error(f"Llama OCR failed: {e}")
            return ""

    def extract_raw_text(self, image_path: str, engine: Optional[str] = None) -> str:
        """
        Extract raw text from image using specified OCR engine.

        Args:
            image_path: Path to image file
            engine: OCR engine to use (None for preferred)

        Returns:
            Extracted raw text
        """
        if engine is None:
            engine = self.preferred_engine

        if engine not in self.available_engines:
            logger.warning(f"Engine {engine} not available, trying alternatives")
            if self.available_engines:
                engine = self.available_engines[0]
            else:
                logger.error("No OCR engines available")
                return ""

        logger.info(f"Extracting text using {engine}")

        if engine == "tesseract":
            return self.extract_text_tesseract(image_path)
        elif engine == "easyocr":
            return self.extract_text_easyocr(image_path)
        elif engine == "paddleocr":
            return self.extract_text_paddleocr(image_path)
        elif engine == "llama_ocr":
            return self.extract_text_llama_ocr(image_path)
        else:
            logger.error(f"Unknown OCR engine: {engine}")
            return ""

    def parse_text_with_llm(self, raw_text: str) -> CheckElements:
        """
        Parse extracted text using LLM to identify check elements.

        Args:
            raw_text: Raw OCR text

        Returns:
            CheckElements with parsed information
        """
        if not raw_text.strip():
            logger.warning("No text to parse")
            return CheckElements(raw_text=raw_text, confidence_score=0.0)

        try:
            # Create parsing prompt
            prompt = f"""
            You are an expert at analyzing check text extracted by OCR. The following text was extracted from a check image:

            OCR TEXT:
            {raw_text}

            Please analyze this text and extract the following check information:

            1. Payee (Pay to the order of): The person/entity receiving payment
            2. Amount in words: The written amount (e.g., "One Thousand Five Hundred Dollars")
            3. Amount in digits: The numerical amount (e.g., 1500.00)
            4. Date: The date on the check
            5. Account Number: The account number (usually a long number sequence, 10-15 digits)
            6. Check Number: The check number
            7. Signature: Whether text suggests a signature is present (look for names, initials)
            8. Bank Name: The name of the bank

            IMPORTANT INSTRUCTIONS:
            - Look for COMPLETE account numbers (10-15 digits), not partial numbers
            - For amounts, look for both written form and numerical form
            - Be very careful with account numbers - extract the full sequence
            - If you see multiple numbers, the account number is usually the longest one
            - Look for signature indicators like names written in a different style

            Please provide the response in this EXACT format:
            PAYEE: [extracted payee or NOT_FOUND]
            AMOUNT_WORDS: [amount in words or NOT_FOUND]
            AMOUNT_DIGITS: [numerical amount or NOT_FOUND]
            DATE: [date or NOT_FOUND]
            ACCOUNT_NUMBER: [complete account number or NOT_FOUND]
            CHECK_NUMBER: [check number or NOT_FOUND]
            SIGNATURE_PRESENT: [yes/no]
            BANK_NAME: [bank name or NOT_FOUND]
            """

            # Call LLM
            response = self.ollama_client.chat(
                model=self.llm_model,
                messages=[
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ]
            )

            # Parse response
            llm_response = response['message']['content']
            elements = self._parse_llm_response(llm_response, raw_text)

            logger.info("Successfully parsed OCR text with LLM")
            return elements

        except Exception as e:
            logger.error(f"LLM parsing failed: {e}")
            return CheckElements(raw_text=raw_text, confidence_score=0.0)

    def _parse_llm_response(self, response_text: str, raw_text: str) -> CheckElements:
        """Parse LLM response into CheckElements."""
        elements = CheckElements(raw_text=raw_text)

        try:
            # Extract each field using regex
            patterns = {
                'payee': r'PAYEE:\s*(.+?)(?:\n|$)',
                'amount_words': r'AMOUNT_WORDS:\s*(.+?)(?:\n|$)',
                'amount_digits': r'AMOUNT_DIGITS:\s*(.+?)(?:\n|$)',
                'date': r'DATE:\s*(.+?)(?:\n|$)',
                'account_number': r'ACCOUNT_NUMBER:\s*(.+?)(?:\n|$)',
                'check_number': r'CHECK_NUMBER:\s*(.+?)(?:\n|$)',
                'signature_present': r'SIGNATURE_PRESENT:\s*(.+?)(?:\n|$)',
                'bank_name': r'BANK_NAME:\s*(.+?)(?:\n|$)'
            }

            for field, pattern in patterns.items():
                match = re.search(pattern, response_text, re.IGNORECASE)
                if match:
                    value = match.group(1).strip()
                    if value and value.upper() != "NOT_FOUND":
                        if field == 'amount_digits':
                            # Clean and convert amount to float
                            cleaned_amount = re.sub(r'[^\d.]', '', value)
                            try:
                                elements.amount_digits = float(cleaned_amount)
                            except ValueError:
                                elements.amount_digits = None
                        elif field == 'signature_present':
                            elements.signature_present = value.lower() in ['yes', 'true', '1']
                        else:
                            setattr(elements, field, value)

            # Calculate confidence score based on extracted fields
            total_fields = 8
            extracted_fields = sum([
                1 for field in ['payee', 'amount_words', 'amount_digits', 'date',
                               'account_number', 'check_number', 'bank_name']
                if getattr(elements, field) is not None
            ]) + (1 if elements.signature_present else 0)

            elements.confidence_score = extracted_fields / total_fields

        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")
            elements.confidence_score = 0.0

        return elements

    def extract_elements(self, image_path: str, engine: Optional[str] = None) -> CheckElements:
        """
        Complete extraction pipeline: OCR + LLM parsing.

        Args:
            image_path: Path to check image
            engine: OCR engine to use

        Returns:
            CheckElements with extracted information
        """
        logger.info(f"Starting OCR+LLM extraction for: {image_path}")

        # Step 1: Extract raw text with OCR
        raw_text = self.extract_raw_text(image_path, engine)

        if not raw_text.strip():
            logger.warning("No text extracted by OCR")
            return CheckElements(raw_text="", confidence_score=0.0)

        logger.info(f"OCR extracted {len(raw_text)} characters")

        # Step 2: Parse text with LLM
        elements = self.parse_text_with_llm(raw_text)

        return elements

    def get_available_engines(self) -> List[str]:
        """Get list of available OCR engines."""
        return self.available_engines

    def test_engine(self, engine: str, image_path: str) -> Dict[str, Any]:
        """Test a specific OCR engine on an image."""
        if engine not in self.available_engines:
            return {"error": f"Engine {engine} not available"}

        try:
            raw_text = self.extract_raw_text(image_path, engine)
            return {
                "engine": engine,
                "success": True,
                "text_length": len(raw_text),
                "preview": raw_text[:200] + "..." if len(raw_text) > 200 else raw_text
            }
        except Exception as e:
            return {
                "engine": engine,
                "success": False,
                "error": str(e)
            }
