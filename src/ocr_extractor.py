"""
OCR-based text extraction with multiple engine support.
"""
import cv2
import numpy as np
from PIL import Image
import logging
import re
import ollama
from typing import Optional, Dict, Any, List, Tuple
from .models import CheckElements

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OCRExtractor:
    """Extract text using OCR engines and parse with LLM."""
    
    def __init__(self, preferred_engine: str = "tesseract", llm_model: str = "llama3.2:latest"):
        """
        Initialize OCR extractor.
        
        Args:
            preferred_engine: OCR engine to use ('tesseract', 'easyocr', 'paddleocr')
            llm_model: LLM model for text parsing
        """
        self.preferred_engine = preferred_engine
        self.llm_model = llm_model
        self.available_engines = self._check_available_engines()
        self.ollama_client = ollama.Client()
        
        logger.info(f"OCR Extractor initialized with engine: {preferred_engine}")
        logger.info(f"Available OCR engines: {self.available_engines}")
    
    def _check_available_engines(self) -> List[str]:
        """Check which OCR engines are available."""
        available = []
        
        # Check Tesseract
        try:
            import pytesseract
            available.append("tesseract")
        except ImportError:
            logger.warning("Tesseract not available")
        
        # Check EasyOCR
        try:
            import easyocr
            available.append("easyocr")
        except ImportError:
            logger.warning("EasyOCR not available")
        
        # Check PaddleOCR
        try:
            from paddleocr import PaddleOCR
            available.append("paddleocr")
        except ImportError:
            logger.warning("PaddleOCR not available")
        
        return available
    
    def extract_text_tesseract(self, image_path: str) -> str:
        """Extract text using Tesseract OCR."""
        try:
            import pytesseract
            
            # Load and preprocess image
            img = cv2.imread(image_path)
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Apply preprocessing for better OCR
            # Denoise
            denoised = cv2.fastNlMeansDenoising(gray)
            
            # Enhance contrast
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(denoised)
            
            # Threshold
            _, thresh = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # Extract text with custom config
            custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,/$-: '
            text = pytesseract.image_to_string(thresh, config=custom_config)
            
            return text.strip()
            
        except Exception as e:
            logger.error(f"Tesseract OCR failed: {e}")
            return ""
    
    def extract_text_easyocr(self, image_path: str) -> str:
        """Extract text using EasyOCR."""
        try:
            import easyocr
            
            # Initialize reader (English)
            reader = easyocr.Reader(['en'])
            
            # Extract text
            results = reader.readtext(image_path)
            
            # Combine all detected text
            text_lines = []
            for (bbox, text, confidence) in results:
                if confidence > 0.5:  # Filter low confidence detections
                    text_lines.append(text)
            
            return '\n'.join(text_lines)
            
        except Exception as e:
            logger.error(f"EasyOCR failed: {e}")
            return ""
    
    def extract_text_paddleocr(self, image_path: str) -> str:
        """Extract text using PaddleOCR."""
        try:
            from paddleocr import PaddleOCR
            
            # Initialize PaddleOCR
            ocr = PaddleOCR(use_angle_cls=True, lang='en', show_log=False)
            
            # Extract text
            results = ocr.ocr(image_path, cls=True)
            
            # Combine all detected text
            text_lines = []
            for line in results[0]:
                if line[1][1] > 0.5:  # Filter low confidence detections
                    text_lines.append(line[1][0])
            
            return '\n'.join(text_lines)
            
        except Exception as e:
            logger.error(f"PaddleOCR failed: {e}")
            return ""
    
    def extract_raw_text(self, image_path: str, engine: Optional[str] = None) -> str:
        """
        Extract raw text from image using specified OCR engine.
        
        Args:
            image_path: Path to image file
            engine: OCR engine to use (None for preferred)
            
        Returns:
            Extracted raw text
        """
        if engine is None:
            engine = self.preferred_engine
        
        if engine not in self.available_engines:
            logger.warning(f"Engine {engine} not available, trying alternatives")
            if self.available_engines:
                engine = self.available_engines[0]
            else:
                logger.error("No OCR engines available")
                return ""
        
        logger.info(f"Extracting text using {engine}")
        
        if engine == "tesseract":
            return self.extract_text_tesseract(image_path)
        elif engine == "easyocr":
            return self.extract_text_easyocr(image_path)
        elif engine == "paddleocr":
            return self.extract_text_paddleocr(image_path)
        else:
            logger.error(f"Unknown OCR engine: {engine}")
            return ""
    
    def parse_text_with_llm(self, raw_text: str) -> CheckElements:
        """
        Parse extracted text using LLM to identify check elements.
        
        Args:
            raw_text: Raw OCR text
            
        Returns:
            CheckElements with parsed information
        """
        if not raw_text.strip():
            logger.warning("No text to parse")
            return CheckElements(raw_text=raw_text, confidence_score=0.0)
        
        try:
            # Create parsing prompt
            prompt = f"""
            You are an expert at analyzing check text extracted by OCR. The following text was extracted from a check image:

            OCR TEXT:
            {raw_text}

            Please analyze this text and extract the following check information:

            1. Payee (Pay to the order of): The person/entity receiving payment
            2. Amount in words: The written amount (e.g., "One Thousand Five Hundred Dollars")
            3. Amount in digits: The numerical amount (e.g., 1500.00)
            4. Date: The date on the check
            5. Account Number: The account number (usually a long number sequence, 10-15 digits)
            6. Check Number: The check number
            7. Signature: Whether text suggests a signature is present (look for names, initials)
            8. Bank Name: The name of the bank

            IMPORTANT INSTRUCTIONS:
            - Look for COMPLETE account numbers (10-15 digits), not partial numbers
            - For amounts, look for both written form and numerical form
            - Be very careful with account numbers - extract the full sequence
            - If you see multiple numbers, the account number is usually the longest one
            - Look for signature indicators like names written in a different style

            Please provide the response in this EXACT format:
            PAYEE: [extracted payee or NOT_FOUND]
            AMOUNT_WORDS: [amount in words or NOT_FOUND]
            AMOUNT_DIGITS: [numerical amount or NOT_FOUND]
            DATE: [date or NOT_FOUND]
            ACCOUNT_NUMBER: [complete account number or NOT_FOUND]
            CHECK_NUMBER: [check number or NOT_FOUND]
            SIGNATURE_PRESENT: [yes/no]
            BANK_NAME: [bank name or NOT_FOUND]
            """
            
            # Call LLM
            response = self.ollama_client.chat(
                model=self.llm_model,
                messages=[
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ]
            )
            
            # Parse response
            llm_response = response['message']['content']
            elements = self._parse_llm_response(llm_response, raw_text)
            
            logger.info("Successfully parsed OCR text with LLM")
            return elements
            
        except Exception as e:
            logger.error(f"LLM parsing failed: {e}")
            return CheckElements(raw_text=raw_text, confidence_score=0.0)
    
    def _parse_llm_response(self, response_text: str, raw_text: str) -> CheckElements:
        """Parse LLM response into CheckElements."""
        elements = CheckElements(raw_text=raw_text)
        
        try:
            # Extract each field using regex
            patterns = {
                'payee': r'PAYEE:\s*(.+?)(?:\n|$)',
                'amount_words': r'AMOUNT_WORDS:\s*(.+?)(?:\n|$)',
                'amount_digits': r'AMOUNT_DIGITS:\s*(.+?)(?:\n|$)',
                'date': r'DATE:\s*(.+?)(?:\n|$)',
                'account_number': r'ACCOUNT_NUMBER:\s*(.+?)(?:\n|$)',
                'check_number': r'CHECK_NUMBER:\s*(.+?)(?:\n|$)',
                'signature_present': r'SIGNATURE_PRESENT:\s*(.+?)(?:\n|$)',
                'bank_name': r'BANK_NAME:\s*(.+?)(?:\n|$)'
            }
            
            for field, pattern in patterns.items():
                match = re.search(pattern, response_text, re.IGNORECASE)
                if match:
                    value = match.group(1).strip()
                    if value and value.upper() != "NOT_FOUND":
                        if field == 'amount_digits':
                            # Clean and convert amount to float
                            cleaned_amount = re.sub(r'[^\d.]', '', value)
                            try:
                                elements.amount_digits = float(cleaned_amount)
                            except ValueError:
                                elements.amount_digits = None
                        elif field == 'signature_present':
                            elements.signature_present = value.lower() in ['yes', 'true', '1']
                        else:
                            setattr(elements, field, value)
            
            # Calculate confidence score based on extracted fields
            total_fields = 8
            extracted_fields = sum([
                1 for field in ['payee', 'amount_words', 'amount_digits', 'date', 
                               'account_number', 'check_number', 'bank_name']
                if getattr(elements, field) is not None
            ]) + (1 if elements.signature_present else 0)
            
            elements.confidence_score = extracted_fields / total_fields
            
        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")
            elements.confidence_score = 0.0
        
        return elements
    
    def extract_elements(self, image_path: str, engine: Optional[str] = None) -> CheckElements:
        """
        Complete extraction pipeline: OCR + LLM parsing.
        
        Args:
            image_path: Path to check image
            engine: OCR engine to use
            
        Returns:
            CheckElements with extracted information
        """
        logger.info(f"Starting OCR+LLM extraction for: {image_path}")
        
        # Step 1: Extract raw text with OCR
        raw_text = self.extract_raw_text(image_path, engine)
        
        if not raw_text.strip():
            logger.warning("No text extracted by OCR")
            return CheckElements(raw_text="", confidence_score=0.0)
        
        logger.info(f"OCR extracted {len(raw_text)} characters")
        
        # Step 2: Parse text with LLM
        elements = self.parse_text_with_llm(raw_text)
        
        return elements
    
    def get_available_engines(self) -> List[str]:
        """Get list of available OCR engines."""
        return self.available_engines
    
    def test_engine(self, engine: str, image_path: str) -> Dict[str, Any]:
        """Test a specific OCR engine on an image."""
        if engine not in self.available_engines:
            return {"error": f"Engine {engine} not available"}
        
        try:
            raw_text = self.extract_raw_text(image_path, engine)
            return {
                "engine": engine,
                "success": True,
                "text_length": len(raw_text),
                "preview": raw_text[:200] + "..." if len(raw_text) > 200 else raw_text
            }
        except Exception as e:
            return {
                "engine": engine,
                "success": False,
                "error": str(e)
            }
