"""
Customer behavioral analysis for fraud detection.
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from src.models import Transaction, CustomerProfile, FraudReason
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BehavioralAnalyzer:
    """Analyze customer behavior patterns to detect fraud."""

    def __init__(self, transaction_data: Optional[pd.DataFrame] = None):
        """
        Initialize the behavioral analyzer.

        Args:
            transaction_data: Historical transaction data for analysis
        """
        self.transaction_data = transaction_data
        self.customer_profiles = {}

        if transaction_data is not None:
            self._build_customer_profiles()

    def _build_customer_profiles(self):
        """Build customer profiles from transaction data."""
        if self.transaction_data is None:
            return

        for customer_id in self.transaction_data['customer_id'].unique():
            customer_data = self.transaction_data[
                self.transaction_data['customer_id'] == customer_id
            ]

            profile = self._create_customer_profile(customer_data)
            self.customer_profiles[customer_id] = profile

        logger.info(f"Built profiles for {len(self.customer_profiles)} customers")

    def _create_customer_profile(self, customer_data: pd.DataFrame) -> CustomerProfile:
        """Create a customer profile from transaction history."""
        customer_id = customer_data['customer_id'].iloc[0]

        # Calculate statistics
        amounts = customer_data['amount']
        avg_amount = amounts.mean()
        max_amount = amounts.max()

        # Common payees (top 5)
        payee_counts = customer_data['payee'].value_counts()
        common_payees = payee_counts.head(5).index.tolist()

        # Transaction frequency
        date_range = (customer_data['date'].max() - customer_data['date'].min()).days
        frequency = len(customer_data) / max(date_range, 1)

        # Preferred categories
        if 'category' in customer_data.columns:
            category_counts = customer_data['category'].value_counts()
            preferred_categories = category_counts.head(3).index.tolist()
        else:
            preferred_categories = []

        # Account age (assume oldest transaction represents account opening)
        account_age = (datetime.now() - customer_data['date'].min()).days

        return CustomerProfile(
            customer_id=customer_id,
            avg_transaction_amount=avg_amount,
            max_transaction_amount=max_amount,
            common_payees=common_payees,
            transaction_frequency=frequency,
            preferred_categories=preferred_categories,
            account_age_days=account_age
        )

    def analyze_transaction(self, transaction: Transaction) -> Dict[str, Any]:
        """
        Analyze a transaction for behavioral anomalies.

        Args:
            transaction: Transaction to analyze

        Returns:
            Dictionary with analysis results
        """
        analysis = {
            'anomalies': [],
            'risk_factors': [],
            'behavioral_score': 0.0,
            'recommendations': []
        }

        # Get customer profile
        profile = self.customer_profiles.get(transaction.customer_id)
        if not profile:
            analysis['anomalies'].append('unknown_customer')
            analysis['behavioral_score'] = 0.8
            analysis['recommendations'].append('Verify customer identity')
            return analysis

        # Analyze amount anomalies
        amount_analysis = self._analyze_amount(transaction, profile)
        analysis.update(amount_analysis)

        # Analyze payee patterns
        payee_analysis = self._analyze_payee(transaction, profile)
        analysis.update(payee_analysis)

        # Analyze frequency patterns
        frequency_analysis = self._analyze_frequency(transaction, profile)
        analysis.update(frequency_analysis)

        # Analyze timing patterns
        timing_analysis = self._analyze_timing(transaction, profile)
        analysis.update(timing_analysis)

        # Calculate overall behavioral score
        analysis['behavioral_score'] = self._calculate_behavioral_score(analysis)

        return analysis

    def _analyze_amount(self, transaction: Transaction, profile: CustomerProfile) -> Dict[str, Any]:
        """Analyze transaction amount against customer's normal patterns."""
        analysis = {'amount_anomalies': [], 'amount_risk_factors': []}

        # Check if amount is significantly higher than average
        if transaction.amount > profile.avg_transaction_amount * 3:
            analysis['amount_anomalies'].append('excessive_amount')
            analysis['amount_risk_factors'].append(
                f"Amount ${transaction.amount:.2f} is {transaction.amount/profile.avg_transaction_amount:.1f}x higher than average"
            )

        # Check if amount exceeds historical maximum
        if transaction.amount > profile.max_transaction_amount * 1.5:
            analysis['amount_anomalies'].append('exceeds_historical_max')
            analysis['amount_risk_factors'].append(
                f"Amount exceeds historical maximum of ${profile.max_transaction_amount:.2f}"
            )

        # Check for round numbers (potential fraud indicator)
        if transaction.amount % 100 == 0 and transaction.amount >= 1000:
            analysis['amount_risk_factors'].append("Round number amount (potential fraud indicator)")

        return analysis

    def _analyze_payee(self, transaction: Transaction, profile: CustomerProfile) -> Dict[str, Any]:
        """Analyze payee against customer's normal patterns."""
        analysis = {'payee_anomalies': [], 'payee_risk_factors': []}

        # Check if payee is in customer's common payees
        if transaction.payee not in profile.common_payees:
            analysis['payee_anomalies'].append('unknown_payee')
            analysis['payee_risk_factors'].append(
                f"Payee '{transaction.payee}' not in customer's common payees"
            )

        # Check for suspicious payee patterns
        suspicious_keywords = ['cash', 'loan', 'debt', 'urgent', 'emergency']
        if any(keyword in transaction.payee.lower() for keyword in suspicious_keywords):
            analysis['payee_risk_factors'].append("Payee contains suspicious keywords")

        return analysis

    def _analyze_frequency(self, transaction: Transaction, profile: CustomerProfile) -> Dict[str, Any]:
        """Analyze transaction frequency patterns."""
        analysis = {'frequency_anomalies': [], 'frequency_risk_factors': []}

        if self.transaction_data is None:
            return analysis

        # Get recent transactions for this customer
        recent_cutoff = datetime.now() - timedelta(days=7)
        recent_transactions = self.transaction_data[
            (self.transaction_data['customer_id'] == transaction.customer_id) &
            (self.transaction_data['date'] >= recent_cutoff)
        ]

        # Check for unusual frequency
        recent_count = len(recent_transactions)
        expected_weekly = profile.transaction_frequency * 7

        if recent_count > expected_weekly * 2:
            analysis['frequency_anomalies'].append('high_frequency')
            analysis['frequency_risk_factors'].append(
                f"Recent transaction frequency ({recent_count}) exceeds normal pattern"
            )

        return analysis

    def _analyze_timing(self, transaction: Transaction, profile: CustomerProfile) -> Dict[str, Any]:
        """Analyze transaction timing patterns."""
        analysis = {'timing_anomalies': [], 'timing_risk_factors': []}

        # Check for unusual hours (if timestamp available)
        hour = transaction.date.hour
        if hour < 6 or hour > 22:  # Outside normal business hours
            analysis['timing_risk_factors'].append("Transaction outside normal business hours")

        # Check for weekend transactions (higher risk for business accounts)
        if transaction.date.weekday() >= 5:  # Saturday or Sunday
            analysis['timing_risk_factors'].append("Weekend transaction")

        return analysis

    def _calculate_behavioral_score(self, analysis: Dict[str, Any]) -> float:
        """Calculate overall behavioral risk score (0.0 = low risk, 1.0 = high risk)."""
        score = 0.0

        # Count anomalies
        anomaly_count = 0
        for key in analysis:
            if 'anomalies' in key and isinstance(analysis[key], list):
                anomaly_count += len(analysis[key])

        # Count risk factors
        risk_factor_count = 0
        for key in analysis:
            if 'risk_factors' in key and isinstance(analysis[key], list):
                risk_factor_count += len(analysis[key])

        # Calculate score based on anomalies and risk factors
        score = min(1.0, (anomaly_count * 0.3) + (risk_factor_count * 0.1))

        return score

    def get_customer_risk_profile(self, customer_id: str) -> Dict[str, Any]:
        """Get comprehensive risk profile for a customer."""
        profile = self.customer_profiles.get(customer_id)
        if not profile:
            return {'error': 'Customer not found'}

        if self.transaction_data is None:
            return {'error': 'No transaction data available'}

        customer_data = self.transaction_data[
            self.transaction_data['customer_id'] == customer_id
        ]

        return {
            'customer_id': customer_id,
            'total_transactions': len(customer_data),
            'avg_amount': profile.avg_transaction_amount,
            'max_amount': profile.max_transaction_amount,
            'common_payees': profile.common_payees,
            'transaction_frequency': profile.transaction_frequency,
            'account_age_days': profile.account_age_days,
            'risk_indicators': self._identify_risk_indicators(customer_data, profile)
        }

    def _identify_risk_indicators(self, customer_data: pd.DataFrame,
                                 profile: CustomerProfile) -> List[str]:
        """Identify risk indicators for a customer."""
        indicators = []

        # High-value transactions
        high_value_count = len(customer_data[
            customer_data['amount'] > profile.avg_transaction_amount * 5
        ])
        if high_value_count > len(customer_data) * 0.1:  # More than 10%
            indicators.append("Frequent high-value transactions")

        # New account
        if profile.account_age_days < 90:
            indicators.append("New account (less than 90 days)")

        # High transaction frequency
        if profile.transaction_frequency > 3:  # More than 3 per day
            indicators.append("High transaction frequency")

        return indicators
