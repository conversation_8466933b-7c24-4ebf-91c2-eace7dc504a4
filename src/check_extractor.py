"""
Check element extraction using Vision LLM through Ollama.
"""
import ollama
import base64
from PIL import Image
import io
import re
from typing import Optional, Dict, Any
from src.models import CheckElements
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CheckExtractor:
    """Extract elements from check images using Vision LLM."""

    def __init__(self, model_name: str = "llava:latest"):
        """
        Initialize the check extractor.

        Args:
            model_name: Name of the vision model to use with Ollama
        """
        self.model_name = model_name
        self.client = ollama.Client()

    def _encode_image(self, image_path: str) -> str:
        """
        Encode image to base64 string.

        Args:
            image_path: Path to the image file

        Returns:
            Base64 encoded image string
        """
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # Resize if too large (optional optimization)
                max_size = (1024, 1024)
                img.thumbnail(max_size, Image.Resampling.LANCZOS)

                # Convert to base64
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG')
                img_str = base64.b64encode(buffer.getvalue()).decode()
                return img_str
        except Exception as e:
            logger.error(f"Error encoding image: {e}")
            raise

    def extract_elements(self, image_path: str) -> CheckElements:
        """
        Extract elements from a check image.

        Args:
            image_path: Path to the check image

        Returns:
            CheckElements object with extracted information
        """
        try:
            # Encode the image
            image_b64 = self._encode_image(image_path)

            # Create the prompt for check analysis
            prompt = """
            Analyze this check image and extract the following information in a structured format.
            Pay special attention to the account number at the bottom of the check - it's usually a long number sequence.

            1. Payee (Pay to the order of): The person/entity receiving payment
            2. Amount in words: The written amount (e.g., "Eighty Eight Lakhs")
            3. Amount in digits: The numerical amount (e.g., 88,00,000)
            4. Date: The date on the check
            5. Account Number: The FULL account number at the bottom (usually 10-15 digits, look carefully)
            6. Check Number: The check number (usually top right)
            7. Signature: Whether a signature is present (yes/no)
            8. Bank Name: The name of the bank

            IMPORTANT: For the account number, look at the bottom of the check for the MICR line.
            The account number is typically the longest number sequence, often 10-15 digits.
            Extract the COMPLETE number, not just part of it.

            Please provide the response in this exact format:
            PAYEE: [extracted payee]
            AMOUNT_WORDS: [amount in words]
            AMOUNT_DIGITS: [numerical amount]
            DATE: [date]
            ACCOUNT_NUMBER: [complete account number from bottom of check]
            CHECK_NUMBER: [check number]
            SIGNATURE_PRESENT: [yes/no]
            BANK_NAME: [bank name]

            If any field cannot be clearly identified, write "NOT_FOUND" for that field.
            """

            # Call the vision model
            response = self.client.chat(
                model=self.model_name,
                messages=[
                    {
                        'role': 'user',
                        'content': prompt,
                        'images': [image_b64]
                    }
                ]
            )

            # Parse the response
            extracted_text = response['message']['content']
            elements = self._parse_response(extracted_text)

            logger.info(f"Successfully extracted elements from check: {image_path}")
            return elements

        except Exception as e:
            logger.error(f"Error extracting elements from check {image_path}: {e}")
            return CheckElements(raw_text=str(e), confidence_score=0.0)

    def _parse_response(self, response_text: str) -> CheckElements:
        """
        Parse the LLM response into CheckElements.

        Args:
            response_text: Raw response from the LLM

        Returns:
            CheckElements object
        """
        elements = CheckElements(raw_text=response_text)

        try:
            # Extract each field using regex
            patterns = {
                'payee': r'PAYEE:\s*(.+?)(?:\n|$)',
                'amount_words': r'AMOUNT_WORDS:\s*(.+?)(?:\n|$)',
                'amount_digits': r'AMOUNT_DIGITS:\s*(.+?)(?:\n|$)',
                'date': r'DATE:\s*(.+?)(?:\n|$)',
                'account_number': r'ACCOUNT_NUMBER:\s*(.+?)(?:\n|$)',
                'check_number': r'CHECK_NUMBER:\s*(.+?)(?:\n|$)',
                'signature_present': r'SIGNATURE_PRESENT:\s*(.+?)(?:\n|$)',
                'bank_name': r'BANK_NAME:\s*(.+?)(?:\n|$)'
            }

            for field, pattern in patterns.items():
                match = re.search(pattern, response_text, re.IGNORECASE)
                if match:
                    value = match.group(1).strip()
                    if value and value.upper() != "NOT_FOUND":
                        if field == 'amount_digits':
                            # Clean and convert amount to float
                            cleaned_amount = re.sub(r'[^\d.]', '', value)
                            try:
                                elements.amount_digits = float(cleaned_amount)
                            except ValueError:
                                elements.amount_digits = None
                        elif field == 'signature_present':
                            elements.signature_present = value.lower() in ['yes', 'true', '1']
                        else:
                            setattr(elements, field, value)

            # Calculate confidence score based on extracted fields
            total_fields = 8
            extracted_fields = sum([
                1 for field in ['payee', 'amount_words', 'amount_digits', 'date',
                               'account_number', 'check_number', 'bank_name']
                if getattr(elements, field) is not None
            ]) + (1 if elements.signature_present else 0)

            elements.confidence_score = extracted_fields / total_fields

        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")
            elements.confidence_score = 0.0

        return elements
