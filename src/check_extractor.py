"""
Check element extraction using Vision LLM or OCR+LLM through Ollama.
"""
import ollama
import base64
from PIL import Image
import io
import re
from typing import Optional, Dict, Any
from src.models import CheckElements
from src.ocr_extractor import OCRExtractor
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CheckExtractor:
    """Extract elements from check images using Vision LLM or OCR+LLM."""

    def __init__(self, model_name: str = "llava:latest",
                 extraction_mode: str = "vision",
                 ocr_engine: str = "tesseract",
                 llm_model: str = "llama3.2:latest"):
        """
        Initialize the check extractor.

        Args:
            model_name: Name of the vision model to use with Ollama
            extraction_mode: 'vision' for Vision LLM, 'ocr' for OCR+LLM
            ocr_engine: OCR engine to use ('tesseract', 'easyocr', 'paddleocr')
            llm_model: LLM model for text parsing in OCR mode
        """
        self.model_name = model_name
        self.extraction_mode = extraction_mode
        self.client = ollama.Client()

        # Initialize OCR extractor if needed
        if extraction_mode == "ocr":
            self.ocr_extractor = OCRExtractor(ocr_engine, llm_model)
        else:
            self.ocr_extractor = None

    def _encode_image(self, image_path: str) -> str:
        """
        Encode image to base64 string.

        Args:
            image_path: Path to the image file

        Returns:
            Base64 encoded image string
        """
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # Resize if too large (optional optimization)
                max_size = (1024, 1024)
                img.thumbnail(max_size, Image.Resampling.LANCZOS)

                # Convert to base64
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG')
                img_str = base64.b64encode(buffer.getvalue()).decode()
                return img_str
        except Exception as e:
            logger.error(f"Error encoding image: {e}")
            raise

    def extract_elements(self, image_path: str, mode: Optional[str] = None) -> CheckElements:
        """
        Extract elements from a check image.

        Args:
            image_path: Path to the check image
            mode: Override extraction mode ('vision' or 'ocr')

        Returns:
            CheckElements object with extracted information
        """
        extraction_mode = mode or self.extraction_mode

        logger.info(f"Extracting elements using {extraction_mode} mode")

        if extraction_mode == "ocr":
            return self._extract_with_ocr(image_path)
        else:
            return self._extract_with_vision(image_path)

    def _extract_with_vision(self, image_path: str) -> CheckElements:
        """Extract elements using Vision LLM."""
        try:
            # Encode the image
            image_b64 = self._encode_image(image_path)

            # Create the prompt for check analysis
            prompt = """
            Analyze this check image and extract the following information in a structured format.
            Pay special attention to the account number at the bottom of the check - it's usually a long number sequence.

            1. Payee (Pay to the order of): The person/entity receiving payment
            2. Amount in words: The written amount (e.g., "Eighty Eight Lakhs")
            3. Amount in digits: The numerical amount (e.g., 88,00,000)
            4. Date: The date on the check
            5. Account Number: The FULL account number at the bottom (usually 10-15 digits, look carefully)
            6. Check Number: The check number (usually top right)
            7. Signature: Whether a signature is present (yes/no)
            8. Bank Name: The name of the bank

            IMPORTANT: For the account number, look at the bottom of the check for the MICR line.
            The account number is typically the longest number sequence, often 10-15 digits.
            Extract the COMPLETE number, not just part of it.

            Please provide the response in this exact format:
            PAYEE: [extracted payee]
            AMOUNT_WORDS: [amount in words]
            AMOUNT_DIGITS: [numerical amount]
            DATE: [date]
            ACCOUNT_NUMBER: [complete account number from bottom of check]
            CHECK_NUMBER: [check number]
            SIGNATURE_PRESENT: [yes/no]
            BANK_NAME: [bank name]

            If any field cannot be clearly identified, write "NOT_FOUND" for that field.
            """

            # Call the vision model
            response = self.client.chat(
                model=self.model_name,
                messages=[
                    {
                        'role': 'user',
                        'content': prompt,
                        'images': [image_b64]
                    }
                ]
            )

            # Parse the response
            extracted_text = response['message']['content']
            elements = self._parse_response(extracted_text)

            logger.info(f"Successfully extracted elements from check using Vision LLM: {image_path}")
            return elements

        except Exception as e:
            logger.error(f"Error extracting elements from check {image_path}: {e}")
            return CheckElements(raw_text=str(e), confidence_score=0.0)

    def _extract_with_ocr(self, image_path: str) -> CheckElements:
        """Extract elements using OCR + LLM."""
        try:
            if self.ocr_extractor is None:
                # Initialize OCR extractor if not already done
                self.ocr_extractor = OCRExtractor()

            elements = self.ocr_extractor.extract_elements(image_path)
            logger.info(f"Successfully extracted elements from check using OCR+LLM: {image_path}")
            return elements

        except Exception as e:
            logger.error(f"Error extracting elements with OCR from check {image_path}: {e}")
            return CheckElements(raw_text=str(e), confidence_score=0.0)

    def _parse_response(self, response_text: str) -> CheckElements:
        """
        Parse the LLM response into CheckElements.

        Args:
            response_text: Raw response from the LLM

        Returns:
            CheckElements object
        """
        elements = CheckElements(raw_text=response_text)

        try:
            # Extract each field using regex
            patterns = {
                'payee': r'PAYEE:\s*(.+?)(?:\n|$)',
                'amount_words': r'AMOUNT_WORDS:\s*(.+?)(?:\n|$)',
                'amount_digits': r'AMOUNT_DIGITS:\s*(.+?)(?:\n|$)',
                'date': r'DATE:\s*(.+?)(?:\n|$)',
                'account_number': r'ACCOUNT_NUMBER:\s*(.+?)(?:\n|$)',
                'check_number': r'CHECK_NUMBER:\s*(.+?)(?:\n|$)',
                'signature_present': r'SIGNATURE_PRESENT:\s*(.+?)(?:\n|$)',
                'bank_name': r'BANK_NAME:\s*(.+?)(?:\n|$)'
            }

            for field, pattern in patterns.items():
                match = re.search(pattern, response_text, re.IGNORECASE)
                if match:
                    value = match.group(1).strip()
                    if value and value.upper() != "NOT_FOUND":
                        if field == 'amount_digits':
                            # Clean and convert amount to float
                            cleaned_amount = re.sub(r'[^\d.]', '', value)
                            try:
                                elements.amount_digits = float(cleaned_amount)
                            except ValueError:
                                elements.amount_digits = None
                        elif field == 'signature_present':
                            elements.signature_present = value.lower() in ['yes', 'true', '1']
                        else:
                            setattr(elements, field, value)

            # Calculate confidence score based on extracted fields
            total_fields = 8
            extracted_fields = sum([
                1 for field in ['payee', 'amount_words', 'amount_digits', 'date',
                               'account_number', 'check_number', 'bank_name']
                if getattr(elements, field) is not None
            ]) + (1 if elements.signature_present else 0)

            elements.confidence_score = extracted_fields / total_fields

        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")
            elements.confidence_score = 0.0

        return elements

    def get_available_modes(self) -> Dict[str, Any]:
        """Get available extraction modes and OCR engines."""
        modes = {
            "vision": {
                "available": True,
                "description": "Vision Language Model (LLaVA)",
                "model": self.model_name
            },
            "ocr": {
                "available": False,
                "description": "OCR + Language Model",
                "engines": []
            }
        }

        # Check OCR availability
        try:
            if self.ocr_extractor is None:
                temp_extractor = OCRExtractor()
            else:
                temp_extractor = self.ocr_extractor

            available_engines = temp_extractor.get_available_engines()
            modes["ocr"]["available"] = len(available_engines) > 0
            modes["ocr"]["engines"] = available_engines

        except Exception as e:
            logger.warning(f"Could not check OCR availability: {e}")

        return modes

    def set_extraction_mode(self, mode: str, ocr_engine: str = None):
        """Change extraction mode."""
        if mode not in ["vision", "ocr"]:
            raise ValueError("Mode must be 'vision' or 'ocr'")

        self.extraction_mode = mode

        if mode == "ocr":
            if self.ocr_extractor is None or (ocr_engine and ocr_engine != self.ocr_extractor.preferred_engine):
                self.ocr_extractor = OCRExtractor(ocr_engine or "tesseract")

        logger.info(f"Extraction mode set to: {mode}")

    def test_extraction_modes(self, image_path: str) -> Dict[str, Any]:
        """Test both extraction modes on an image."""
        results = {}

        # Test Vision LLM
        try:
            vision_result = self._extract_with_vision(image_path)
            results["vision"] = {
                "success": True,
                "confidence": vision_result.confidence_score,
                "elements_found": sum([
                    1 for field in ['payee', 'amount_words', 'amount_digits', 'date',
                                   'account_number', 'check_number', 'bank_name']
                    if getattr(vision_result, field) is not None
                ])
            }
        except Exception as e:
            results["vision"] = {"success": False, "error": str(e)}

        # Test OCR + LLM
        try:
            ocr_result = self._extract_with_ocr(image_path)
            results["ocr"] = {
                "success": True,
                "confidence": ocr_result.confidence_score,
                "elements_found": sum([
                    1 for field in ['payee', 'amount_words', 'amount_digits', 'date',
                                   'account_number', 'check_number', 'bank_name']
                    if getattr(ocr_result, field) is not None
                ])
            }
        except Exception as e:
            results["ocr"] = {"success": False, "error": str(e)}

        return results