"""
Main fraud detection engine that combines multiple detection methods.
"""
import re
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any, Optional
import pandas as pd
from src.models import CheckElements, FraudDetectionResult, FraudReason, Transaction
from src.check_extractor import CheckExtractor
from src.behavioral_analyzer import BehavioralAnalyzer
from src.signature_analyzer import SignatureAnalyzer
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FraudDetector:
    """Main fraud detection engine."""

    def __init__(self, transaction_data: Optional[pd.DataFrame] = None,
                 model_name: str = "llava:latest", signature_db_path: str = "sig"):
        """
        Initialize the fraud detector.

        Args:
            transaction_data: Historical transaction data for behavioral analysis
            model_name: Vision model name for check extraction
            signature_db_path: Path to signature database directory
        """
        self.check_extractor = CheckExtractor(model_name)
        self.behavioral_analyzer = BehavioralAnalyzer(transaction_data)
        self.signature_analyzer = SignatureAnalyzer(signature_db_path)
        self.transaction_data = transaction_data

        # Fraud detection thresholds
        self.thresholds = {
            'amount_mismatch_tolerance': 0.01,  # 1% tolerance for amount differences
            'behavioral_risk_threshold': 0.6,   # Risk score threshold
            'confidence_threshold': 0.7,        # Minimum confidence for extraction
            'max_amount_multiplier': 10,        # Max amount vs average multiplier
            'duplicate_check_window_days': 30   # Days to check for duplicate checks
        }

    def detect_fraud(self, check_image_path: str, customer_id: str) -> FraudDetectionResult:
        """
        Perform comprehensive fraud detection on a check.

        Args:
            check_image_path: Path to the check image
            customer_id: Customer ID for behavioral analysis

        Returns:
            FraudDetectionResult with comprehensive analysis
        """
        logger.info(f"Starting fraud detection for check: {check_image_path}")

        # Extract elements from check
        extracted_elements = self.check_extractor.extract_elements(check_image_path)

        # Initialize result
        fraud_reasons = []
        risk_score = 0.0
        recommendations = []
        behavioral_analysis = {}

        # Check extraction confidence
        if extracted_elements.confidence_score < self.thresholds['confidence_threshold']:
            fraud_reasons.append(FraudReason.SUSPICIOUS_SIGNATURE)
            recommendations.append("Manual review required - low extraction confidence")
            risk_score += 0.3

        # Perform multi-criteria fraud detection

        # 1. Amount mismatch detection
        amount_analysis = self._detect_amount_mismatch(extracted_elements)
        if amount_analysis['is_mismatch']:
            fraud_reasons.append(FraudReason.AMOUNT_MISMATCH)
            recommendations.extend(amount_analysis['recommendations'])
            risk_score += 0.4

        # 2. Date validation
        date_analysis = self._validate_date(extracted_elements)
        if not date_analysis['is_valid']:
            fraud_reasons.append(FraudReason.INVALID_DATE)
            recommendations.extend(date_analysis['recommendations'])
            risk_score += 0.3

        # 3. Duplicate check detection
        if self.transaction_data is not None:
            duplicate_analysis = self._detect_duplicate_check(extracted_elements, customer_id)
            if duplicate_analysis['is_duplicate']:
                fraud_reasons.append(FraudReason.DUPLICATE_CHECK)
                recommendations.extend(duplicate_analysis['recommendations'])
                risk_score += 0.5

        # 4. Behavioral analysis
        if extracted_elements.amount_digits is not None:
            transaction = Transaction(
                customer_id=customer_id,
                transaction_id=f"CHECK_{extracted_elements.check_number or 'UNKNOWN'}",
                amount=extracted_elements.amount_digits,
                payee=extracted_elements.payee or "UNKNOWN",
                date=datetime.now(),
                transaction_type="CHECK",
                account_number=extracted_elements.account_number or "",
                check_number=extracted_elements.check_number
            )

            behavioral_analysis = self.behavioral_analyzer.analyze_transaction(transaction)

            if behavioral_analysis['behavioral_score'] > self.thresholds['behavioral_risk_threshold']:
                fraud_reasons.append(FraudReason.UNUSUAL_BEHAVIOR)
                recommendations.append("Unusual behavioral pattern detected")
                risk_score += behavioral_analysis['behavioral_score'] * 0.4

        # 5. Enhanced signature analysis
        signature_analysis = self._analyze_signature(check_image_path, extracted_elements)
        if signature_analysis['is_suspicious']:
            fraud_reasons.append(FraudReason.SUSPICIOUS_SIGNATURE)
            recommendations.extend(signature_analysis['recommendations'])
            risk_score += signature_analysis['risk_increase']

        # 6. Excessive amount detection
        excessive_amount_analysis = self._detect_excessive_amount(
            extracted_elements, customer_id
        )
        if excessive_amount_analysis['is_excessive']:
            fraud_reasons.append(FraudReason.EXCESSIVE_AMOUNT)
            recommendations.extend(excessive_amount_analysis['recommendations'])
            risk_score += 0.3

        # Calculate final fraud determination
        risk_score = min(1.0, risk_score)  # Cap at 1.0
        is_fraud = len(fraud_reasons) > 0 or risk_score > 0.5
        confidence_score = 1.0 - abs(0.5 - risk_score) * 2  # Higher confidence at extremes

        # Add general recommendations
        if is_fraud:
            recommendations.append("Recommend manual review and verification")
            if risk_score > 0.8:
                recommendations.append("High risk - consider blocking transaction")
        else:
            recommendations.append("Transaction appears legitimate")

        result = FraudDetectionResult(
            is_fraud=is_fraud,
            confidence_score=confidence_score,
            fraud_reasons=fraud_reasons,
            risk_score=risk_score,
            extracted_elements=extracted_elements,
            behavioral_analysis=behavioral_analysis,
            recommendations=recommendations
        )

        # Add signature analysis to result (store as additional attribute)
        result.signature_analysis = signature_analysis

        logger.info(f"Fraud detection completed. Is fraud: {is_fraud}, Risk score: {risk_score:.2f}")
        return result

    def _detect_amount_mismatch(self, elements: CheckElements) -> Dict[str, Any]:
        """Detect mismatch between written and numeric amounts."""
        analysis = {
            'is_mismatch': False,
            'recommendations': [],
            'details': {}
        }

        if not elements.amount_words or elements.amount_digits is None:
            analysis['recommendations'].append("Unable to verify amount consistency - missing data")
            return analysis

        # Convert written amount to number (simplified)
        written_amount = self._parse_written_amount(elements.amount_words)

        if written_amount is not None:
            difference = abs(written_amount - elements.amount_digits)
            tolerance = elements.amount_digits * self.thresholds['amount_mismatch_tolerance']

            if difference > tolerance:
                analysis['is_mismatch'] = True
                analysis['recommendations'].append(
                    f"Amount mismatch: Written '{elements.amount_words}' "
                    f"vs numeric '{elements.amount_digits}'"
                )

            analysis['details'] = {
                'written_amount': written_amount,
                'numeric_amount': elements.amount_digits,
                'difference': difference,
                'tolerance': tolerance
            }
        else:
            analysis['recommendations'].append("Unable to parse written amount")

        return analysis

    def _parse_written_amount(self, amount_text: str) -> Optional[float]:
        """Parse written amount to numeric value (simplified implementation)."""
        if not amount_text:
            return None

        # This is a simplified parser - in production, you'd want a more robust solution
        amount_text = amount_text.lower().strip()

        # Common number words mapping
        number_words = {
            'zero': 0, 'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5,
            'six': 6, 'seven': 7, 'eight': 8, 'nine': 9, 'ten': 10,
            'eleven': 11, 'twelve': 12, 'thirteen': 13, 'fourteen': 14, 'fifteen': 15,
            'sixteen': 16, 'seventeen': 17, 'eighteen': 18, 'nineteen': 19, 'twenty': 20,
            'thirty': 30, 'forty': 40, 'fifty': 50, 'sixty': 60, 'seventy': 70,
            'eighty': 80, 'ninety': 90, 'hundred': 100, 'thousand': 1000,
            'lakh': 100000, 'lakhs': 100000, 'million': 1000000
        }

        # Extract numbers from text
        total = 0
        current = 0

        words = re.findall(r'\b\w+\b', amount_text)

        for word in words:
            if word in number_words:
                value = number_words[word]
                if value == 100:
                    current *= 100
                elif value >= 1000:
                    total += current * value
                    current = 0
                else:
                    current += value

        total += current
        return float(total) if total > 0 else None

    def _validate_date(self, elements: CheckElements) -> Dict[str, Any]:
        """Validate check date."""
        analysis = {
            'is_valid': True,
            'recommendations': [],
            'parsed_date': None
        }

        if not elements.date:
            analysis['is_valid'] = False
            analysis['recommendations'].append("No date found on check")
            return analysis

        # Try to parse the date
        try:
            # Common date formats
            date_formats = ['%d-%m-%Y', '%m/%d/%Y', '%Y-%m-%d', '%d/%m/%Y']
            parsed_date = None

            for fmt in date_formats:
                try:
                    parsed_date = datetime.strptime(elements.date.strip(), fmt)
                    break
                except ValueError:
                    continue

            if parsed_date is None:
                # Try to extract date with regex
                date_match = re.search(r'(\d{1,2})[/-](\d{1,2})[/-](\d{2,4})', elements.date)
                if date_match:
                    day, month, year = date_match.groups()
                    if len(year) == 2:
                        year = '20' + year if int(year) < 50 else '19' + year
                    parsed_date = datetime(int(year), int(month), int(day))

            if parsed_date:
                analysis['parsed_date'] = parsed_date

                # Check if date is in the future
                if parsed_date > datetime.now():
                    analysis['is_valid'] = False
                    analysis['recommendations'].append("Check date is in the future")

                # Check if date is too old (more than 6 months)
                if parsed_date < datetime.now() - timedelta(days=180):
                    analysis['is_valid'] = False
                    analysis['recommendations'].append("Check date is more than 6 months old")
            else:
                analysis['is_valid'] = False
                analysis['recommendations'].append("Unable to parse check date")

        except Exception as e:
            analysis['is_valid'] = False
            analysis['recommendations'].append(f"Date validation error: {str(e)}")

        return analysis

    def _detect_duplicate_check(self, elements: CheckElements, customer_id: str) -> Dict[str, Any]:
        """Detect duplicate check numbers."""
        analysis = {
            'is_duplicate': False,
            'recommendations': []
        }

        if not elements.check_number or self.transaction_data is None:
            return analysis

        # Check for duplicate check numbers in recent transactions
        cutoff_date = datetime.now() - timedelta(days=self.thresholds['duplicate_check_window_days'])

        recent_checks = self.transaction_data[
            (self.transaction_data['customer_id'] == customer_id) &
            (self.transaction_data['check_number'] == elements.check_number) &
            (self.transaction_data['date'] >= cutoff_date)
        ]

        if len(recent_checks) > 0:
            analysis['is_duplicate'] = True
            analysis['recommendations'].append(
                f"Duplicate check number {elements.check_number} found in recent transactions"
            )

        return analysis

    def _detect_excessive_amount(self, elements: CheckElements, customer_id: str) -> Dict[str, Any]:
        """Detect excessive amounts based on customer history."""
        analysis = {
            'is_excessive': False,
            'recommendations': []
        }

        if elements.amount_digits is None:
            return analysis

        # Get customer profile
        profile = self.behavioral_analyzer.customer_profiles.get(customer_id)
        if not profile:
            # No history available, use general thresholds
            if elements.amount_digits > 10000:  # $10,000 threshold
                analysis['is_excessive'] = True
                analysis['recommendations'].append("High amount for unknown customer")
            return analysis

        # Check against customer's historical patterns
        if elements.amount_digits > profile.max_transaction_amount * self.thresholds['max_amount_multiplier']:
            analysis['is_excessive'] = True
            analysis['recommendations'].append(
                f"Amount ${elements.amount_digits:.2f} significantly exceeds "
                f"customer's historical maximum of ${profile.max_transaction_amount:.2f}"
            )

        return analysis

    def _analyze_signature(self, check_image_path: str, elements: CheckElements) -> Dict[str, Any]:
        """Analyze signature for fraud indicators."""
        analysis = {
            'is_suspicious': False,
            'recommendations': [],
            'risk_increase': 0.0,
            'signature_comparison': {},
            'details': {}
        }

        try:
            # Basic signature presence check
            if not elements.signature_present:
                analysis['is_suspicious'] = True
                analysis['recommendations'].append("No signature detected on check")
                analysis['risk_increase'] = 0.3
                analysis['details']['signature_present'] = False
                return analysis

            analysis['details']['signature_present'] = True

            # Extract signature from check
            extracted_signature = self.signature_analyzer.extract_signature_from_check(check_image_path)

            if extracted_signature is None or extracted_signature.size == 0:
                analysis['is_suspicious'] = True
                analysis['recommendations'].append("Could not extract signature for analysis")
                analysis['risk_increase'] = 0.2
                analysis['details']['extraction_failed'] = True
                return analysis

            # If we have an account number, try to compare with reference signature
            if elements.account_number:
                comparison_result = self.signature_analyzer.compare_signatures(
                    extracted_signature, elements.account_number
                )
                analysis['signature_comparison'] = comparison_result

                if comparison_result['reference_available']:
                    if not comparison_result['match_found']:
                        analysis['is_suspicious'] = True
                        analysis['recommendations'].append(
                            f"Signature does not match reference for account {elements.account_number}"
                        )
                        analysis['risk_increase'] = 0.5
                    elif comparison_result['similarity_score'] < 0.7:
                        analysis['is_suspicious'] = True
                        analysis['recommendations'].append(
                            f"Signature similarity low ({comparison_result['similarity_score']:.2f})"
                        )
                        analysis['risk_increase'] = 0.3
                    else:
                        analysis['recommendations'].append(
                            f"Signature matches reference (similarity: {comparison_result['similarity_score']:.2f})"
                        )
                else:
                    analysis['recommendations'].append(
                        f"No reference signature available for account {elements.account_number}"
                    )
                    analysis['risk_increase'] = 0.1  # Small risk increase for unknown signature
            else:
                analysis['recommendations'].append("Cannot verify signature - no account number extracted")
                analysis['risk_increase'] = 0.1

            # Save extracted signature for debugging (optional)
            if extracted_signature.size > 0:
                debug_path = f"output/extracted_signature_{elements.account_number or 'unknown'}.png"
                self.signature_analyzer.save_extracted_signature(extracted_signature, debug_path)
                analysis['details']['extracted_signature_saved'] = debug_path

        except Exception as e:
            logger.error(f"Error in signature analysis: {e}")
            analysis['is_suspicious'] = True
            analysis['recommendations'].append(f"Signature analysis failed: {str(e)}")
            analysis['risk_increase'] = 0.2
            analysis['details']['error'] = str(e)

        return analysis