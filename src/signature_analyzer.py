"""
Signature extraction and comparison module.
"""
import os
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFilter
import base64
import io
from typing import Optional, Dict, Any, Tuple
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SignatureAnalyzer:
    """Extract and compare signatures from check images."""
    
    def __init__(self, signature_db_path: str = "sig"):
        """
        Initialize signature analyzer.
        
        Args:
            signature_db_path: Path to directory containing reference signatures
        """
        self.signature_db_path = signature_db_path
        self.signature_cache = {}
        self._load_signature_database()
    
    def _load_signature_database(self):
        """Load reference signatures from the database directory."""
        if not os.path.exists(self.signature_db_path):
            os.makedirs(self.signature_db_path, exist_ok=True)
            logger.warning(f"Signature database directory created: {self.signature_db_path}")
            return
        
        signature_files = [f for f in os.listdir(self.signature_db_path) 
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        
        for sig_file in signature_files:
            account_number = os.path.splitext(sig_file)[0]
            sig_path = os.path.join(self.signature_db_path, sig_file)
            
            try:
                # Load and preprocess reference signature
                ref_signature = self._preprocess_signature(sig_path)
                self.signature_cache[account_number] = {
                    'path': sig_path,
                    'processed': ref_signature,
                    'features': self._extract_signature_features(ref_signature)
                }
                logger.info(f"Loaded reference signature for account: {account_number}")
            except Exception as e:
                logger.error(f"Failed to load signature for {account_number}: {e}")
        
        logger.info(f"Loaded {len(self.signature_cache)} reference signatures")
    
    def extract_signature_from_check(self, check_image_path: str) -> Optional[np.ndarray]:
        """
        Extract signature region from check image.
        
        Args:
            check_image_path: Path to check image
            
        Returns:
            Extracted signature as numpy array or None if not found
        """
        try:
            # Load image
            img = cv2.imread(check_image_path)
            if img is None:
                logger.error(f"Could not load image: {check_image_path}")
                return None
            
            # Convert to grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Get image dimensions
            height, width = gray.shape
            
            # Define signature region (typically bottom-right area of check)
            # Adjust these coordinates based on typical check layout
            sig_region_x = int(width * 0.5)  # Right half
            sig_region_y = int(height * 0.6)  # Bottom 40%
            sig_region_w = int(width * 0.45)  # 45% width
            sig_region_h = int(height * 0.3)   # 30% height
            
            # Extract signature region
            signature_region = gray[sig_region_y:sig_region_y + sig_region_h,
                                  sig_region_x:sig_region_x + sig_region_w]
            
            # Preprocess the extracted region
            processed_signature = self._preprocess_signature_region(signature_region)
            
            return processed_signature
            
        except Exception as e:
            logger.error(f"Error extracting signature from {check_image_path}: {e}")
            return None
    
    def _preprocess_signature(self, signature_path: str) -> np.ndarray:
        """Preprocess reference signature image."""
        img = cv2.imread(signature_path, cv2.IMREAD_GRAYSCALE)
        return self._preprocess_signature_region(img)
    
    def _preprocess_signature_region(self, signature_region: np.ndarray) -> np.ndarray:
        """
        Preprocess signature region for comparison.
        
        Args:
            signature_region: Raw signature region
            
        Returns:
            Preprocessed signature
        """
        if signature_region is None or signature_region.size == 0:
            return np.array([])
        
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(signature_region, (3, 3), 0)
        
        # Apply adaptive thresholding to get binary image
        binary = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                     cv2.THRESH_BINARY_INV, 11, 2)
        
        # Remove small noise using morphological operations
        kernel = np.ones((2, 2), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)
        
        # Resize to standard size for comparison
        if cleaned.shape[0] > 0 and cleaned.shape[1] > 0:
            cleaned = cv2.resize(cleaned, (200, 100))
        
        return cleaned
    
    def _extract_signature_features(self, signature: np.ndarray) -> Dict[str, Any]:
        """
        Extract features from signature for comparison.
        
        Args:
            signature: Preprocessed signature image
            
        Returns:
            Dictionary of signature features
        """
        if signature.size == 0:
            return {}
        
        features = {}
        
        # Basic geometric features
        contours, _ = cv2.findContours(signature, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if contours:
            # Largest contour (main signature)
            main_contour = max(contours, key=cv2.contourArea)
            
            # Contour area
            features['area'] = cv2.contourArea(main_contour)
            
            # Contour perimeter
            features['perimeter'] = cv2.arcLength(main_contour, True)
            
            # Bounding rectangle
            x, y, w, h = cv2.boundingRect(main_contour)
            features['aspect_ratio'] = w / h if h > 0 else 0
            features['extent'] = features['area'] / (w * h) if w * h > 0 else 0
            
            # Convex hull
            hull = cv2.convexHull(main_contour)
            features['solidity'] = features['area'] / cv2.contourArea(hull) if cv2.contourArea(hull) > 0 else 0
        
        # Pixel density
        total_pixels = signature.shape[0] * signature.shape[1]
        white_pixels = np.sum(signature == 255)
        features['density'] = white_pixels / total_pixels if total_pixels > 0 else 0
        
        # Moments for shape analysis
        moments = cv2.moments(signature)
        if moments['m00'] != 0:
            features['centroid_x'] = moments['m10'] / moments['m00']
            features['centroid_y'] = moments['m01'] / moments['m00']
        else:
            features['centroid_x'] = 0
            features['centroid_y'] = 0
        
        return features
    
    def compare_signatures(self, extracted_signature: np.ndarray, 
                          account_number: str) -> Dict[str, Any]:
        """
        Compare extracted signature with reference signature.
        
        Args:
            extracted_signature: Signature extracted from check
            account_number: Account number to find reference signature
            
        Returns:
            Comparison results
        """
        result = {
            'match_found': False,
            'similarity_score': 0.0,
            'confidence': 0.0,
            'reference_available': False,
            'details': {}
        }
        
        # Check if reference signature exists
        if account_number not in self.signature_cache:
            result['details']['error'] = f"No reference signature found for account {account_number}"
            return result
        
        result['reference_available'] = True
        reference_data = self.signature_cache[account_number]
        reference_signature = reference_data['processed']
        
        if extracted_signature.size == 0:
            result['details']['error'] = "No signature extracted from check"
            return result
        
        try:
            # Template matching
            template_score = self._template_matching(extracted_signature, reference_signature)
            
            # Feature comparison
            extracted_features = self._extract_signature_features(extracted_signature)
            reference_features = reference_data['features']
            feature_score = self._compare_features(extracted_features, reference_features)
            
            # Structural similarity
            structural_score = self._structural_similarity(extracted_signature, reference_signature)
            
            # Combined similarity score
            similarity_score = (template_score * 0.3 + feature_score * 0.4 + structural_score * 0.3)
            
            result['similarity_score'] = similarity_score
            result['confidence'] = min(similarity_score * 1.2, 1.0)  # Boost confidence slightly
            result['match_found'] = similarity_score > 0.6  # Threshold for match
            
            result['details'] = {
                'template_score': template_score,
                'feature_score': feature_score,
                'structural_score': structural_score,
                'extracted_features': extracted_features,
                'reference_features': reference_features
            }
            
        except Exception as e:
            result['details']['error'] = f"Error during comparison: {str(e)}"
            logger.error(f"Signature comparison error: {e}")
        
        return result
    
    def _template_matching(self, extracted: np.ndarray, reference: np.ndarray) -> float:
        """Perform template matching between signatures."""
        try:
            # Ensure both images are the same size
            if extracted.shape != reference.shape:
                extracted = cv2.resize(extracted, (reference.shape[1], reference.shape[0]))
            
            # Normalized cross-correlation
            result = cv2.matchTemplate(extracted, reference, cv2.TM_CCOEFF_NORMED)
            return float(np.max(result))
        except:
            return 0.0
    
    def _compare_features(self, features1: Dict[str, Any], features2: Dict[str, Any]) -> float:
        """Compare signature features."""
        if not features1 or not features2:
            return 0.0
        
        common_keys = set(features1.keys()) & set(features2.keys())
        if not common_keys:
            return 0.0
        
        similarities = []
        for key in common_keys:
            val1, val2 = features1[key], features2[key]
            if val1 == 0 and val2 == 0:
                similarities.append(1.0)
            elif val1 == 0 or val2 == 0:
                similarities.append(0.0)
            else:
                # Normalized difference
                diff = abs(val1 - val2) / max(abs(val1), abs(val2))
                similarities.append(1.0 - diff)
        
        return np.mean(similarities) if similarities else 0.0
    
    def _structural_similarity(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """Calculate structural similarity between images."""
        try:
            # Ensure same size
            if img1.shape != img2.shape:
                img1 = cv2.resize(img1, (img2.shape[1], img2.shape[0]))
            
            # Simple structural similarity based on pixel differences
            diff = cv2.absdiff(img1, img2)
            similarity = 1.0 - (np.sum(diff) / (255.0 * img1.size))
            return max(0.0, similarity)
        except:
            return 0.0
    
    def save_extracted_signature(self, signature: np.ndarray, filename: str):
        """Save extracted signature for debugging."""
        if signature.size > 0:
            cv2.imwrite(filename, signature)
            logger.info(f"Saved extracted signature: {filename}")
    
    def get_available_accounts(self) -> list:
        """Get list of accounts with reference signatures."""
        return list(self.signature_cache.keys())
