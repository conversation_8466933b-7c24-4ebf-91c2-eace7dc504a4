"""
Utility functions for the fraud detection system.
"""
import os
import json
import logging
from typing import Dict, Any, List
import pandas as pd
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def setup_directories():
    """Create necessary directories for the project."""
    directories = [
        'sample_data',
        'tests',
        'logs',
        'output'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"Created directory: {directory}")


def save_results_to_json(results: Dict[str, Any], filename: str):
    """
    Save fraud detection results to JSON file.
    
    Args:
        results: Results dictionary to save
        filename: Output filename
    """
    try:
        os.makedirs('output', exist_ok=True)
        filepath = os.path.join('output', filename)
        
        with open(filepath, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Results saved to {filepath}")
        return filepath
    except Exception as e:
        logger.error(f"Error saving results: {e}")
        return None


def load_transaction_data(filepath: str) -> pd.DataFrame:
    """
    Load transaction data from CSV file.
    
    Args:
        filepath: Path to the CSV file
        
    Returns:
        DataFrame with transaction data
    """
    try:
        df = pd.read_csv(filepath)
        df['date'] = pd.to_datetime(df['date'])
        logger.info(f"Loaded {len(df)} transactions from {filepath}")
        return df
    except Exception as e:
        logger.error(f"Error loading transaction data: {e}")
        return pd.DataFrame()


def validate_check_image(image_path: str) -> bool:
    """
    Validate that the check image exists and is readable.
    
    Args:
        image_path: Path to the check image
        
    Returns:
        True if valid, False otherwise
    """
    if not os.path.exists(image_path):
        logger.error(f"Check image not found: {image_path}")
        return False
    
    # Check file extension
    valid_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    _, ext = os.path.splitext(image_path.lower())
    
    if ext not in valid_extensions:
        logger.error(f"Invalid image format: {ext}")
        return False
    
    # Check file size (should be reasonable)
    file_size = os.path.getsize(image_path)
    if file_size > 50 * 1024 * 1024:  # 50MB limit
        logger.error(f"Image file too large: {file_size} bytes")
        return False
    
    if file_size < 1024:  # 1KB minimum
        logger.error(f"Image file too small: {file_size} bytes")
        return False
    
    return True


def format_currency(amount: float) -> str:
    """
    Format amount as currency string.
    
    Args:
        amount: Amount to format
        
    Returns:
        Formatted currency string
    """
    return f"${amount:,.2f}"


def calculate_fraud_statistics(results_list: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Calculate statistics from multiple fraud detection results.
    
    Args:
        results_list: List of fraud detection results
        
    Returns:
        Statistics dictionary
    """
    if not results_list:
        return {}
    
    total_checks = len(results_list)
    fraud_count = sum(1 for r in results_list if r.get('is_fraud', False))
    
    # Calculate average scores
    avg_risk_score = sum(r.get('risk_score', 0) for r in results_list) / total_checks
    avg_confidence = sum(r.get('confidence_score', 0) for r in results_list) / total_checks
    
    # Count fraud reasons
    fraud_reason_counts = {}
    for result in results_list:
        for reason in result.get('fraud_reasons', []):
            fraud_reason_counts[reason] = fraud_reason_counts.get(reason, 0) + 1
    
    return {
        'total_checks_analyzed': total_checks,
        'fraud_detected': fraud_count,
        'fraud_rate': fraud_count / total_checks if total_checks > 0 else 0,
        'average_risk_score': avg_risk_score,
        'average_confidence_score': avg_confidence,
        'fraud_reason_distribution': fraud_reason_counts,
        'analysis_timestamp': datetime.now().isoformat()
    }


def create_fraud_report(results: Dict[str, Any], customer_id: str) -> str:
    """
    Create a human-readable fraud detection report.
    
    Args:
        results: Fraud detection results
        customer_id: Customer ID
        
    Returns:
        Formatted report string
    """
    report = []
    report.append("=" * 60)
    report.append("FRAUD DETECTION REPORT")
    report.append("=" * 60)
    report.append(f"Customer ID: {customer_id}")
    report.append(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # Overall result
    fraud_status = "FRAUD DETECTED" if results.get('is_fraud', False) else "NO FRAUD DETECTED"
    report.append(f"RESULT: {fraud_status}")
    report.append(f"Risk Score: {results.get('risk_score', 0):.2f}/1.00")
    report.append(f"Confidence: {results.get('confidence_score', 0):.2f}/1.00")
    report.append("")
    
    # Extracted elements
    elements = results.get('extracted_elements', {})
    report.append("EXTRACTED CHECK ELEMENTS:")
    report.append("-" * 30)
    report.append(f"Payee: {elements.get('payee', 'N/A')}")
    report.append(f"Amount (digits): {format_currency(elements.get('amount_digits', 0)) if elements.get('amount_digits') else 'N/A'}")
    report.append(f"Amount (words): {elements.get('amount_words', 'N/A')}")
    report.append(f"Date: {elements.get('date', 'N/A')}")
    report.append(f"Check Number: {elements.get('check_number', 'N/A')}")
    report.append(f"Account Number: {elements.get('account_number', 'N/A')}")
    report.append(f"Signature Present: {'Yes' if elements.get('signature_present', False) else 'No'}")
    report.append("")
    
    # Fraud reasons
    fraud_reasons = results.get('fraud_reasons', [])
    if fraud_reasons:
        report.append("FRAUD INDICATORS:")
        report.append("-" * 20)
        for reason in fraud_reasons:
            report.append(f"• {reason.replace('_', ' ').title()}")
        report.append("")
    
    # Recommendations
    recommendations = results.get('recommendations', [])
    if recommendations:
        report.append("RECOMMENDATIONS:")
        report.append("-" * 17)
        for i, rec in enumerate(recommendations, 1):
            report.append(f"{i}. {rec}")
        report.append("")
    
    # Behavioral analysis
    behavioral = results.get('behavioral_analysis', {})
    if behavioral:
        report.append("BEHAVIORAL ANALYSIS:")
        report.append("-" * 21)
        report.append(f"Behavioral Risk Score: {behavioral.get('behavioral_score', 0):.2f}/1.00")
        
        anomalies = []
        for key, value in behavioral.items():
            if 'anomalies' in key and isinstance(value, list):
                anomalies.extend(value)
        
        if anomalies:
            report.append("Behavioral Anomalies:")
            for anomaly in anomalies:
                report.append(f"• {anomaly.replace('_', ' ').title()}")
    
    report.append("=" * 60)
    
    return "\n".join(report)


def check_ollama_connection(model_name: str = "llava:latest") -> bool:
    """
    Check if Ollama is running and the model is available.
    
    Args:
        model_name: Name of the model to check
        
    Returns:
        True if connection is successful, False otherwise
    """
    try:
        import ollama
        client = ollama.Client()
        
        # Try to list models
        models = client.list()
        available_models = [model['name'] for model in models.get('models', [])]
        
        if model_name in available_models:
            logger.info(f"Ollama connection successful. Model '{model_name}' is available.")
            return True
        else:
            logger.warning(f"Model '{model_name}' not found. Available models: {available_models}")
            return False
            
    except Exception as e:
        logger.error(f"Failed to connect to Ollama: {e}")
        return False
