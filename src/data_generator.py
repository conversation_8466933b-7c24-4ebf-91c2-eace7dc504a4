"""
Synthetic transaction data generator for testing and training.
"""
import pandas as pd
import numpy as np
from faker import Faker
from datetime import datetime, timedelta
import random
from typing import List, Dict, Any
from src.models import Transaction, CustomerProfile
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SyntheticDataGenerator:
    """Generate synthetic transaction data for fraud detection testing."""

    def __init__(self, seed: int = 42):
        """
        Initialize the data generator.

        Args:
            seed: Random seed for reproducible results
        """
        self.fake = Faker()
        Faker.seed(seed)
        np.random.seed(seed)
        random.seed(seed)

        # Common transaction categories
        self.categories = [
            'Groceries', 'Gas', 'Restaurant', 'Shopping', 'Utilities',
            'Healthcare', 'Entertainment', 'Travel', 'Education', 'Insurance'
        ]

        # Common payee types
        self.payee_types = {
            'Groceries': ['Walmart', 'Target', 'Kroger', 'Safeway', 'Whole Foods'],
            'Gas': ['Shell', 'Exxon', 'BP', 'Chevron', 'Mobil'],
            'Restaurant': ['McDonald\'s', 'Starbucks', 'Subway', 'Pizza Hut', 'KFC'],
            'Shopping': ['Amazon', 'eBay', 'Best Buy', 'Home Depot', 'Macy\'s'],
            'Utilities': ['Electric Company', 'Gas Company', 'Water Department', 'Internet Provider'],
            'Healthcare': ['City Hospital', 'Family Clinic', 'Pharmacy Plus', 'Dental Care'],
            'Entertainment': ['Netflix', 'Spotify', 'Movie Theater', 'Concert Hall'],
            'Travel': ['Airlines', 'Hotel Chain', 'Car Rental', 'Travel Agency'],
            'Education': ['University', 'Online Course', 'Bookstore', 'Training Center'],
            'Insurance': ['Auto Insurance', 'Health Insurance', 'Life Insurance', 'Home Insurance']
        }

    def generate_customers(self, num_customers: int = 100) -> List[CustomerProfile]:
        """
        Generate synthetic customer profiles.

        Args:
            num_customers: Number of customer profiles to generate

        Returns:
            List of CustomerProfile objects
        """
        customers = []

        for i in range(num_customers):
            customer_id = f"CUST_{i+1:06d}"

            # Generate realistic customer behavior patterns
            avg_amount = np.random.lognormal(mean=3.5, sigma=1.0)  # Average around $30-50
            max_amount = avg_amount * np.random.uniform(5, 20)  # Max 5-20x average

            # Common payees (3-8 regular payees)
            num_payees = random.randint(3, 8)
            common_payees = random.sample([
                payee for payees in self.payee_types.values() for payee in payees
            ], num_payees)

            # Transaction frequency (0.5 to 5 transactions per day)
            frequency = np.random.uniform(0.5, 5.0)

            # Preferred categories
            num_categories = random.randint(2, 5)
            preferred_categories = random.sample(self.categories, num_categories)

            # Account age (30 days to 10 years)
            account_age = random.randint(30, 3650)

            customer = CustomerProfile(
                customer_id=customer_id,
                avg_transaction_amount=avg_amount,
                max_transaction_amount=max_amount,
                common_payees=common_payees,
                transaction_frequency=frequency,
                preferred_categories=preferred_categories,
                account_age_days=account_age
            )

            customers.append(customer)

        logger.info(f"Generated {num_customers} customer profiles")
        return customers

    def generate_transactions(self, customers: List[CustomerProfile],
                            num_transactions: int = 10000,
                            fraud_rate: float = 0.05) -> pd.DataFrame:
        """
        Generate synthetic transaction data.

        Args:
            customers: List of customer profiles
            num_transactions: Total number of transactions to generate
            fraud_rate: Percentage of fraudulent transactions (0.0 to 1.0)

        Returns:
            DataFrame with transaction data
        """
        transactions = []
        num_fraud = int(num_transactions * fraud_rate)
        num_legitimate = num_transactions - num_fraud

        # Generate legitimate transactions
        for _ in range(num_legitimate):
            customer = random.choice(customers)
            transaction = self._generate_legitimate_transaction(customer)
            transactions.append({
                **transaction.__dict__,
                'is_fraud': False,
                'fraud_type': None
            })

        # Generate fraudulent transactions
        for _ in range(num_fraud):
            customer = random.choice(customers)
            transaction, fraud_type = self._generate_fraudulent_transaction(customer)
            transactions.append({
                **transaction.__dict__,
                'is_fraud': True,
                'fraud_type': fraud_type
            })

        # Shuffle transactions
        random.shuffle(transactions)

        # Convert to DataFrame
        df = pd.DataFrame(transactions)
        df['date'] = pd.to_datetime(df['date'])

        logger.info(f"Generated {num_transactions} transactions ({num_fraud} fraudulent)")
        return df

    def _generate_legitimate_transaction(self, customer: CustomerProfile) -> Transaction:
        """Generate a legitimate transaction for a customer."""
        # Amount follows customer's normal pattern
        amount = np.random.normal(
            customer.avg_transaction_amount,
            customer.avg_transaction_amount * 0.3
        )
        amount = max(1.0, amount)  # Minimum $1
        amount = min(amount, customer.max_transaction_amount)  # Respect customer's max

        # Payee from customer's common payees (80% chance) or random (20% chance)
        if random.random() < 0.8 and customer.common_payees:
            payee = random.choice(customer.common_payees)
            category = self._get_category_for_payee(payee)
        else:
            category = random.choice(customer.preferred_categories)
            payee = random.choice(self.payee_types[category])

        # Date within last 90 days
        days_ago = random.randint(0, 90)
        date = datetime.now() - timedelta(days=days_ago)

        return Transaction(
            customer_id=customer.customer_id,
            transaction_id=f"TXN_{self.fake.uuid4()[:8]}",
            amount=round(amount, 2),
            payee=payee,
            date=date,
            transaction_type="CHECK",
            account_number=f"{random.randint(*********, *********)}",
            check_number=f"{random.randint(1000, 9999)}",
            location=self.fake.city(),
            category=category
        )

    def _generate_fraudulent_transaction(self, customer: CustomerProfile) -> tuple:
        """Generate a fraudulent transaction and return fraud type."""
        fraud_types = [
            'excessive_amount', 'unusual_payee', 'frequent_transactions',
            'unusual_time', 'duplicate_check'
        ]

        fraud_type = random.choice(fraud_types)
        transaction = self._generate_legitimate_transaction(customer)

        if fraud_type == 'excessive_amount':
            # Amount much higher than normal
            transaction.amount = customer.max_transaction_amount * random.uniform(2, 5)

        elif fraud_type == 'unusual_payee':
            # Completely random payee not in customer's history
            transaction.payee = self.fake.company()
            transaction.category = 'Unknown'

        elif fraud_type == 'frequent_transactions':
            # Multiple transactions in short time (handled at dataset level)
            pass

        elif fraud_type == 'unusual_time':
            # Transaction at unusual time (very recent or very old)
            if random.random() < 0.5:
                transaction.date = datetime.now() - timedelta(hours=random.randint(0, 2))
            else:
                transaction.date = datetime.now() - timedelta(days=random.randint(180, 365))

        elif fraud_type == 'duplicate_check':
            # Same check number (handled at dataset level)
            pass

        return transaction, fraud_type

    def _get_category_for_payee(self, payee: str) -> str:
        """Get category for a given payee."""
        for category, payees in self.payee_types.items():
            if payee in payees:
                return category
        return random.choice(self.categories)

    def save_to_csv(self, df: pd.DataFrame, filename: str = "synthetic_transactions.csv"):
        """Save transaction data to CSV file."""
        df.to_csv(filename, index=False)
        logger.info(f"Saved {len(df)} transactions to {filename}")
        return filename
