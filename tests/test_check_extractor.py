"""
Tests for the check extractor module.
"""
import pytest
import sys
import os
from unittest.mock import Mo<PERSON>, patch, MagicMock
from PIL import Image
import io
import base64

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.check_extractor import CheckExtractor
from src.models import CheckElements


class TestCheckExtractor:
    """Test cases for CheckExtractor."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.extractor = CheckExtractor()
    
    def create_test_image(self, filename="test_check.jpg"):
        """Create a test image file."""
        # Create a simple test image
        img = Image.new('RGB', (800, 400), color='white')
        img.save(filename)
        return filename
    
    def test_init(self):
        """Test CheckExtractor initialization."""
        extractor = CheckExtractor("test_model")
        assert extractor.model_name == "test_model"
        assert extractor.client is not None
    
    def test_encode_image(self):
        """Test image encoding functionality."""
        # Create test image
        test_image = self.create_test_image()
        
        try:
            encoded = self.extractor._encode_image(test_image)
            assert isinstance(encoded, str)
            assert len(encoded) > 0
            
            # Verify it's valid base64
            decoded = base64.b64decode(encoded)
            assert len(decoded) > 0
        finally:
            # Cleanup
            if os.path.exists(test_image):
                os.remove(test_image)
    
    def test_encode_image_invalid_path(self):
        """Test image encoding with invalid path."""
        with pytest.raises(Exception):
            self.extractor._encode_image("nonexistent_image.jpg")
    
    @patch('ollama.Client')
    def test_extract_elements_success(self, mock_client_class):
        """Test successful element extraction."""
        # Mock Ollama client response
        mock_client = Mock()
        mock_client_class.return_value = mock_client
        
        mock_response = {
            'message': {
                'content': """
                PAYEE: John Doe
                AMOUNT_WORDS: One Thousand Dollars
                AMOUNT_DIGITS: 1000.00
                DATE: 15/03/2024
                ACCOUNT_NUMBER: **********
                CHECK_NUMBER: 1001
                SIGNATURE_PRESENT: yes
                BANK_NAME: Test Bank
                """
            }
        }
        mock_client.chat.return_value = mock_response
        
        # Create test image
        test_image = self.create_test_image()
        
        try:
            # Create new extractor with mocked client
            extractor = CheckExtractor()
            extractor.client = mock_client
            
            result = extractor.extract_elements(test_image)
            
            assert isinstance(result, CheckElements)
            assert result.payee == "John Doe"
            assert result.amount_digits == 1000.00
            assert result.amount_words == "One Thousand Dollars"
            assert result.date == "15/03/2024"
            assert result.account_number == "**********"
            assert result.check_number == "1001"
            assert result.signature_present == True
            assert result.bank_name == "Test Bank"
            assert result.confidence_score > 0
        finally:
            if os.path.exists(test_image):
                os.remove(test_image)
    
    def test_parse_response_complete(self):
        """Test parsing complete LLM response."""
        response_text = """
        PAYEE: Jane Smith
        AMOUNT_WORDS: Five Hundred Dollars
        AMOUNT_DIGITS: 500.00
        DATE: 20/12/2023
        ACCOUNT_NUMBER: **********
        CHECK_NUMBER: 2002
        SIGNATURE_PRESENT: yes
        BANK_NAME: Example Bank
        """
        
        result = self.extractor._parse_response(response_text)
        
        assert result.payee == "Jane Smith"
        assert result.amount_words == "Five Hundred Dollars"
        assert result.amount_digits == 500.00
        assert result.date == "20/12/2023"
        assert result.account_number == "**********"
        assert result.check_number == "2002"
        assert result.signature_present == True
        assert result.bank_name == "Example Bank"
        assert result.confidence_score == 1.0  # All fields present
    
    def test_parse_response_partial(self):
        """Test parsing partial LLM response."""
        response_text = """
        PAYEE: Bob Johnson
        AMOUNT_DIGITS: 750.50
        DATE: NOT_FOUND
        SIGNATURE_PRESENT: no
        """
        
        result = self.extractor._parse_response(response_text)
        
        assert result.payee == "Bob Johnson"
        assert result.amount_digits == 750.50
        assert result.date is None  # NOT_FOUND should be None
        assert result.signature_present == False
        assert result.confidence_score < 1.0  # Partial fields
    
    def test_parse_response_invalid_amount(self):
        """Test parsing response with invalid amount."""
        response_text = """
        PAYEE: Test User
        AMOUNT_DIGITS: invalid_amount
        SIGNATURE_PRESENT: yes
        """
        
        result = self.extractor._parse_response(response_text)
        
        assert result.payee == "Test User"
        assert result.amount_digits is None  # Invalid amount should be None
        assert result.signature_present == True
    
    def test_parse_response_signature_variations(self):
        """Test parsing different signature present values."""
        test_cases = [
            ("yes", True),
            ("true", True),
            ("1", True),
            ("no", False),
            ("false", False),
            ("0", False),
            ("maybe", False)  # Default to False for unclear responses
        ]
        
        for signature_value, expected in test_cases:
            response_text = f"SIGNATURE_PRESENT: {signature_value}"
            result = self.extractor._parse_response(response_text)
            assert result.signature_present == expected
    
    def test_parse_response_empty(self):
        """Test parsing empty response."""
        result = self.extractor._parse_response("")
        
        assert result.payee is None
        assert result.amount_digits is None
        assert result.confidence_score == 0.0
    
    def test_parse_response_malformed(self):
        """Test parsing malformed response."""
        response_text = "This is not a properly formatted response"
        
        result = self.extractor._parse_response(response_text)
        
        # Should handle gracefully
        assert isinstance(result, CheckElements)
        assert result.confidence_score == 0.0
    
    @patch('ollama.Client')
    def test_extract_elements_ollama_error(self, mock_client_class):
        """Test handling of Ollama errors."""
        mock_client = Mock()
        mock_client_class.return_value = mock_client
        mock_client.chat.side_effect = Exception("Ollama connection error")
        
        test_image = self.create_test_image()
        
        try:
            extractor = CheckExtractor()
            extractor.client = mock_client
            
            result = extractor.extract_elements(test_image)
            
            assert isinstance(result, CheckElements)
            assert result.confidence_score == 0.0
            assert "Ollama connection error" in result.raw_text
        finally:
            if os.path.exists(test_image):
                os.remove(test_image)
    
    def test_confidence_score_calculation(self):
        """Test confidence score calculation."""
        # Test with all fields present
        complete_response = """
        PAYEE: Test
        AMOUNT_WORDS: Test
        AMOUNT_DIGITS: 100
        DATE: Test
        ACCOUNT_NUMBER: Test
        CHECK_NUMBER: Test
        SIGNATURE_PRESENT: yes
        BANK_NAME: Test
        """
        result = self.extractor._parse_response(complete_response)
        assert result.confidence_score == 1.0
        
        # Test with half fields present
        partial_response = """
        PAYEE: Test
        AMOUNT_DIGITS: 100
        SIGNATURE_PRESENT: yes
        BANK_NAME: Test
        """
        result = self.extractor._parse_response(partial_response)
        assert result.confidence_score == 0.5  # 4 out of 8 fields
    
    def test_amount_cleaning(self):
        """Test amount cleaning and conversion."""
        test_cases = [
            ("1,000.00", 1000.00),
            ("$500.50", 500.50),
            ("2,500", 2500.00),
            ("invalid", None)
        ]
        
        for amount_text, expected in test_cases:
            response_text = f"AMOUNT_DIGITS: {amount_text}"
            result = self.extractor._parse_response(response_text)
            assert result.amount_digits == expected


if __name__ == "__main__":
    pytest.main([__file__])
