"""
Tests for the synthetic data generator.
"""
import pytest
import pandas as pd
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.data_generator import SyntheticDataGenerator
from src.models import CustomerProfile


class TestSyntheticDataGenerator:
    """Test cases for SyntheticDataGenerator."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.generator = SyntheticDataGenerator(seed=42)
    
    def test_generate_customers(self):
        """Test customer profile generation."""
        num_customers = 10
        customers = self.generator.generate_customers(num_customers)
        
        assert len(customers) == num_customers
        assert all(isinstance(customer, CustomerProfile) for customer in customers)
        
        # Check customer properties
        for customer in customers:
            assert customer.customer_id.startswith("CUST_")
            assert customer.avg_transaction_amount > 0
            assert customer.max_transaction_amount > customer.avg_transaction_amount
            assert len(customer.common_payees) >= 3
            assert customer.transaction_frequency > 0
            assert len(customer.preferred_categories) >= 2
            assert customer.account_age_days >= 30
    
    def test_generate_transactions(self):
        """Test transaction generation."""
        customers = self.generator.generate_customers(5)
        num_transactions = 100
        fraud_rate = 0.1
        
        df = self.generator.generate_transactions(customers, num_transactions, fraud_rate)
        
        assert len(df) == num_transactions
        assert 'is_fraud' in df.columns
        assert 'fraud_type' in df.columns
        
        # Check fraud rate
        fraud_count = df['is_fraud'].sum()
        expected_fraud = int(num_transactions * fraud_rate)
        assert abs(fraud_count - expected_fraud) <= 2  # Allow small variance
        
        # Check required columns
        required_columns = [
            'customer_id', 'transaction_id', 'amount', 'payee', 'date',
            'transaction_type', 'account_number', 'check_number'
        ]
        for col in required_columns:
            assert col in df.columns
        
        # Check data types
        assert df['amount'].dtype in ['float64', 'int64']
        assert pd.api.types.is_datetime64_any_dtype(df['date'])
    
    def test_legitimate_transaction_generation(self):
        """Test legitimate transaction generation."""
        customers = self.generator.generate_customers(1)
        customer = customers[0]
        
        transaction = self.generator._generate_legitimate_transaction(customer)
        
        assert transaction.customer_id == customer.customer_id
        assert transaction.amount > 0
        assert transaction.amount <= customer.max_transaction_amount
        assert transaction.payee is not None
        assert transaction.transaction_type == "CHECK"
        assert transaction.check_number is not None
    
    def test_fraudulent_transaction_generation(self):
        """Test fraudulent transaction generation."""
        customers = self.generator.generate_customers(1)
        customer = customers[0]
        
        transaction, fraud_type = self.generator._generate_fraudulent_transaction(customer)
        
        assert transaction.customer_id == customer.customer_id
        assert fraud_type in [
            'excessive_amount', 'unusual_payee', 'frequent_transactions',
            'unusual_time', 'duplicate_check'
        ]
        
        # Check if fraud characteristics are applied
        if fraud_type == 'excessive_amount':
            assert transaction.amount > customer.max_transaction_amount
        elif fraud_type == 'unusual_payee':
            assert transaction.payee not in customer.common_payees
    
    def test_save_to_csv(self):
        """Test CSV saving functionality."""
        customers = self.generator.generate_customers(2)
        df = self.generator.generate_transactions(customers, 10)
        
        filename = "test_transactions.csv"
        saved_file = self.generator.save_to_csv(df, filename)
        
        assert saved_file == filename
        assert os.path.exists(filename)
        
        # Load and verify
        loaded_df = pd.read_csv(filename)
        assert len(loaded_df) == len(df)
        
        # Cleanup
        os.remove(filename)
    
    def test_category_payee_mapping(self):
        """Test category to payee mapping."""
        test_payees = ['Walmart', 'Shell', 'McDonald\'s']
        
        for payee in test_payees:
            category = self.generator._get_category_for_payee(payee)
            assert category in self.generator.categories
    
    def test_reproducibility(self):
        """Test that results are reproducible with same seed."""
        gen1 = SyntheticDataGenerator(seed=123)
        gen2 = SyntheticDataGenerator(seed=123)
        
        customers1 = gen1.generate_customers(5)
        customers2 = gen2.generate_customers(5)
        
        # Should generate identical customers
        for c1, c2 in zip(customers1, customers2):
            assert c1.customer_id == c2.customer_id
            assert abs(c1.avg_transaction_amount - c2.avg_transaction_amount) < 0.01
    
    def test_data_quality(self):
        """Test data quality and constraints."""
        customers = self.generator.generate_customers(10)
        df = self.generator.generate_transactions(customers, 100)
        
        # Check for missing values in critical columns
        assert df['customer_id'].notna().all()
        assert df['amount'].notna().all()
        assert df['payee'].notna().all()
        assert df['date'].notna().all()
        
        # Check amount constraints
        assert (df['amount'] > 0).all()
        
        # Check customer IDs are valid
        customer_ids = [c.customer_id for c in customers]
        assert df['customer_id'].isin(customer_ids).all()
        
        # Check transaction types
        assert df['transaction_type'].eq('CHECK').all()


if __name__ == "__main__":
    pytest.main([__file__])
