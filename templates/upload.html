{% extends "base.html" %}

{% block title %}Analyze Check - Check Fraud Detection{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-upload me-2"></i>Analyze Check for Fraud</h2>
        <p class="text-muted">Upload a check image to detect potential fraud using AI-powered analysis.</p>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cloud-upload-alt me-2"></i>Upload Check Image</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="uploadForm">
                    <!-- Extraction Mode Selection -->
                    <div class="mb-3">
                        <label for="extraction_mode" class="form-label">Text Extraction Method</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="extraction_mode" id="vision_mode" value="vision" checked>
                                    <label class="form-check-label" for="vision_mode">
                                        <strong>Vision LLM</strong><br>
                                        <small class="text-muted">AI analyzes image directly (faster)</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="extraction_mode" id="ocr_mode" value="ocr">
                                    <label class="form-check-label" for="ocr_mode">
                                        <strong>OCR + LLM</strong><br>
                                        <small class="text-muted">OCR extracts text, then AI parses (more accurate)</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- OCR Engine Selection (shown when OCR mode is selected) -->
                    <div class="mb-3" id="ocr_engine_selection" style="display: none;">
                        <label for="ocr_engine" class="form-label">OCR Engine</label>
                        <select class="form-select" id="ocr_engine" name="ocr_engine">
                            <option value="tesseract">Tesseract (Free, Enhanced for handwriting)</option>
                            <option value="easyocr">EasyOCR (Deep learning, Good for handwriting)</option>
                            <option value="llama_ocr">Llama OCR (AI-powered, Excellent for handwriting)</option>
                            <option value="paddleocr">PaddleOCR (Fast, Good for printed text)</option>
                        </select>
                        <div class="form-text">
                            <strong>For handwritten checks:</strong> Try Llama OCR or EasyOCR for best results.<br>
                            <strong>For printed checks:</strong> Tesseract or PaddleOCR work well.
                        </div>
                    </div>

                    <!-- Customer ID Selection -->
                    <div class="mb-3">
                        <label for="customer_id" class="form-label">Customer ID <small class="text-muted">(Optional)</small></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <input type="text" class="form-control" id="customer_id" name="customer_id"
                                   placeholder="Enter customer ID (e.g., CUST_000001) or leave blank">
                            {% if customers %}
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                    data-bs-toggle="dropdown" aria-expanded="false">
                                Select
                            </button>
                            <ul class="dropdown-menu">
                                {% for customer in customers %}
                                <li><a class="dropdown-item" href="#" onclick="selectCustomer('{{ customer }}')">{{ customer }}</a></li>
                                {% endfor %}
                            </ul>
                            {% endif %}
                        </div>
                        <div class="form-text">
                            <strong>Why Customer ID?</strong> Helps analyze spending patterns for better fraud detection.<br>
                            <strong>Leave blank</strong> for basic check analysis without behavioral comparison.
                        </div>
                    </div>

                    <!-- File Upload Area -->
                    <div class="mb-3">
                        <label for="check_image" class="form-label">Check Image *</label>
                        <div class="upload-area" id="uploadArea">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h5>Drag & Drop Check Image Here</h5>
                            <p class="text-muted">or click to browse files</p>
                            <input type="file" class="form-control" id="check_image" name="check_image"
                                   accept="image/*" required style="display: none;">
                            <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('check_image').click()">
                                <i class="fas fa-folder-open me-2"></i>Browse Files
                            </button>
                        </div>
                        <div class="form-text">
                            Supported formats: JPG, PNG, GIF, BMP, TIFF (Max size: 16MB)
                        </div>
                    </div>

                    <!-- Image Preview -->
                    <div class="mb-3" id="imagePreview" style="display: none;">
                        <label class="form-label">Image Preview</label>
                        <div class="text-center">
                            <img id="previewImg" class="check-preview" alt="Check preview">
                        </div>
                        <div class="mt-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="removeImage()">
                                <i class="fas fa-times me-1"></i>Remove Image
                            </button>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                            <i class="fas fa-search me-2"></i>Analyze Check for Fraud
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Extraction Method Explanation -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Extraction Methods</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12 mb-2">
                        <strong><i class="fas fa-eye text-primary me-1"></i> Vision LLM:</strong>
                        <small class="text-muted d-block">AI directly analyzes the image. Faster but may miss some text details.</small>
                    </div>
                    <div class="col-12 mb-2">
                        <strong><i class="fas fa-text-width text-success me-1"></i> OCR + LLM:</strong>
                        <small class="text-muted d-block">OCR extracts all text first, then AI parses it. Excellent for handwritten checks.</small>
                    </div>
                </div>
                <div class="alert alert-warning py-2 small mt-2">
                    <i class="fas fa-info-circle me-1"></i>
                    <strong>For handwritten checks:</strong> Use OCR + LLM with Llama OCR for best accuracy.
                </div>
            </div>
        </div>

        <!-- Customer ID Explanation -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-question-circle me-2"></i>Why Customer ID?</h6>
            </div>
            <div class="card-body">
                <p class="small mb-2">
                    <strong>With Customer ID:</strong> We analyze the check against the customer's spending history to detect unusual patterns.
                </p>
                <p class="small mb-2">
                    <strong>Without Customer ID:</strong> We perform basic check validation (amounts, dates, signatures) without behavioral analysis.
                </p>
                <div class="alert alert-info py-2 small">
                    <i class="fas fa-lightbulb me-1"></i>
                    <strong>Tip:</strong> Leave Customer ID blank to test basic fraud detection on any check image!
                </div>
            </div>
        </div>

        <!-- Analysis Information -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>What We Analyze</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success me-2"></i>Payee information</li>
                    <li><i class="fas fa-check text-success me-2"></i>Amount consistency (words vs digits)</li>
                    <li><i class="fas fa-check text-success me-2"></i>Date validation</li>
                    <li><i class="fas fa-check text-success me-2"></i>Signature presence</li>
                    <li><i class="fas fa-check text-success me-2"></i>Account and check numbers</li>
                    <li><i class="fas fa-check text-success me-2"></i>Customer behavior patterns <small class="text-muted">(if Customer ID provided)</small></li>
                    <li><i class="fas fa-check text-success me-2"></i>Duplicate check detection <small class="text-muted">(if Customer ID provided)</small></li>
                </ul>
            </div>
        </div>

        <!-- Tips -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Tips for Best Results</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li><i class="fas fa-camera text-primary me-2"></i>Use high-quality, clear images</li>
                    <li><i class="fas fa-sun text-warning me-2"></i>Ensure good lighting</li>
                    <li><i class="fas fa-crop text-info me-2"></i>Capture the entire check</li>
                    <li><i class="fas fa-straighten text-secondary me-2"></i>Keep the check flat and straight</li>
                    <li><i class="fas fa-eye text-success me-2"></i>Make sure all text is readable</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function selectCustomer(customerId) {
    document.getElementById('customer_id').value = customerId;
}

// File upload handling
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('check_image');
const imagePreview = document.getElementById('imagePreview');
const previewImg = document.getElementById('previewImg');
const submitBtn = document.getElementById('submitBtn');

// Drag and drop functionality
uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadArea.classList.add('dragover');
});

uploadArea.addEventListener('dragleave', () => {
    uploadArea.classList.remove('dragover');
});

uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('dragover');

    const files = e.dataTransfer.files;
    if (files.length > 0) {
        fileInput.files = files;
        handleFileSelect();
    }
});

// Click to upload
uploadArea.addEventListener('click', () => {
    fileInput.click();
});

// File input change
fileInput.addEventListener('change', handleFileSelect);

function handleFileSelect() {
    const file = fileInput.files[0];
    if (file) {
        // Validate file type
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/tiff'];
        if (!validTypes.includes(file.type)) {
            alert('Please select a valid image file (JPG, PNG, GIF, BMP, TIFF)');
            fileInput.value = '';
            return;
        }

        // Validate file size (16MB)
        if (file.size > 16 * 1024 * 1024) {
            alert('File size must be less than 16MB');
            fileInput.value = '';
            return;
        }

        // Show preview
        const reader = new FileReader();
        reader.onload = (e) => {
            previewImg.src = e.target.result;
            imagePreview.style.display = 'block';
            uploadArea.style.display = 'none';
        };
        reader.readAsDataURL(file);
    }
}

function removeImage() {
    fileInput.value = '';
    imagePreview.style.display = 'none';
    uploadArea.style.display = 'block';
}

// Form submission
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Analyzing...';
    submitBtn.disabled = true;
});

// Prevent multiple file input clicks
fileInput.addEventListener('click', (e) => {
    e.stopPropagation();
});

// Handle extraction mode changes
document.querySelectorAll('input[name="extraction_mode"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const ocrEngineSelection = document.getElementById('ocr_engine_selection');
        if (this.value === 'ocr') {
            ocrEngineSelection.style.display = 'block';
        } else {
            ocrEngineSelection.style.display = 'none';
        }
    });
});
</script>
{% endblock %}
