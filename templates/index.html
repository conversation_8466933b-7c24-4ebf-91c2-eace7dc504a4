{% extends "base.html" %}

{% block title %}Dashboard - Check Fraud Detection{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="jumbotron bg-primary text-white p-5 rounded mb-4">
            <h1 class="display-4">
                <i class="fas fa-shield-alt me-3"></i>
                Check Fraud Detection System
            </h1>
            <p class="lead">
                AI-powered fraud detection using Vision Language Models and behavioral analysis
            </p>
            <hr class="my-4" style="border-color: rgba(255,255,255,0.3);">
            <p>Upload check images to detect potential fraud using advanced machine learning algorithms.</p>
            <a class="btn btn-light btn-lg" href="{{ url_for('upload_check') }}" role="button">
                <i class="fas fa-upload me-2"></i>Analyze Check Now
            </a>
        </div>
    </div>
</div>

<!-- System Status -->
<div class="row mb-4">
    <div class="col-12">
        <h3><i class="fas fa-heartbeat me-2"></i>System Status</h3>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <div class="status-indicator {{ 'status-online' if ollama_status else 'status-offline' }}"></div>
                <h5 class="card-title">Ollama Vision Model</h5>
                <p class="card-text">
                    {% if ollama_status %}
                        <span class="text-success"><i class="fas fa-check-circle"></i> Connected</span>
                    {% else %}
                        <span class="text-danger"><i class="fas fa-times-circle"></i> Disconnected</span>
                    {% endif %}
                </p>
                {% if not ollama_status %}
                <small class="text-muted">
                    Install Ollama and run: <code>ollama pull llava:latest</code>
                </small>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <div class="status-indicator {{ 'status-online' if system_initialized else 'status-offline' }}"></div>
                <h5 class="card-title">Fraud Detection Engine</h5>
                <p class="card-text">
                    {% if system_initialized %}
                        <span class="text-success"><i class="fas fa-check-circle"></i> Ready</span>
                    {% else %}
                        <span class="text-danger"><i class="fas fa-times-circle"></i> Not Ready</span>
                    {% endif %}
                </p>
                {% if not system_initialized %}
                <small class="text-muted">System initialization failed</small>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <div class="status-indicator status-online"></div>
                <h5 class="card-title">Transaction Data</h5>
                <p class="card-text">
                    <span class="text-success"><i class="fas fa-check-circle"></i> Loaded</span>
                </p>
                {% if stats %}
                <small class="text-muted">
                    {{ "{:,}".format(stats.total_transactions) }} transactions available
                </small>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
{% if stats %}
<div class="row mb-4">
    <div class="col-12">
        <h3><i class="fas fa-chart-bar me-2"></i>Data Statistics</h3>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-receipt fa-2x mb-2"></i>
                <h4>{{ "{:,}".format(stats.total_transactions) }}</h4>
                <p class="mb-0">Total Transactions</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h4>{{ "{:,}".format(stats.total_customers) }}</h4>
                <p class="mb-0">Customers</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h4>{{ "{:.1%}".format(stats.fraud_rate) }}</h4>
                <p class="mb-0">Fraud Rate</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-shield-alt fa-2x mb-2"></i>
                <h4>{{ "{:.1%}".format(1 - stats.fraud_rate) }}</h4>
                <p class="mb-0">Legitimate</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Features -->
<div class="row mb-4">
    <div class="col-12">
        <h3><i class="fas fa-cogs me-2"></i>Detection Features</h3>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-eye text-primary me-2"></i>
                    Vision AI Analysis
                </h5>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Payee extraction</li>
                    <li><i class="fas fa-check text-success me-2"></i>Amount verification (words vs digits)</li>
                    <li><i class="fas fa-check text-success me-2"></i>Date validation</li>
                    <li><i class="fas fa-check text-success me-2"></i>Signature detection</li>
                    <li><i class="fas fa-check text-success me-2"></i>Bank information extraction</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-brain text-info me-2"></i>
                    Behavioral Analysis
                </h5>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Spending pattern analysis</li>
                    <li><i class="fas fa-check text-success me-2"></i>Amount anomaly detection</li>
                    <li><i class="fas fa-check text-success me-2"></i>Payee verification</li>
                    <li><i class="fas fa-check text-success me-2"></i>Transaction frequency analysis</li>
                    <li><i class="fas fa-check text-success me-2"></i>Duplicate check detection</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <h3><i class="fas fa-bolt me-2"></i>Quick Actions</h3>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-upload fa-3x text-primary mb-3"></i>
                <h5 class="card-title">Analyze Check</h5>
                <p class="card-text">Upload a check image for fraud detection analysis</p>
                <a href="{{ url_for('upload_check') }}" class="btn btn-primary">
                    <i class="fas fa-upload me-2"></i>Upload Check
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-database fa-3x text-info mb-3"></i>
                <h5 class="card-title">Generate Data</h5>
                <p class="card-text">Generate new synthetic transaction data for testing</p>
                <a href="{{ url_for('generate_data') }}" class="btn btn-info" 
                   onclick="return confirm('This will generate new transaction data. Continue?')">
                    <i class="fas fa-sync-alt me-2"></i>Generate Data
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-3x text-success mb-3"></i>
                <h5 class="card-title">System Status</h5>
                <p class="card-text">View detailed system status and API information</p>
                <a href="{{ url_for('system_status') }}" target="_blank" class="btn btn-success">
                    <i class="fas fa-external-link-alt me-2"></i>View Status
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
