{% extends "base.html" %}

{% block title %}Analysis Results - Check Fraud Detection{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-search me-2"></i>Fraud Analysis Results</h2>
            <a href="{{ url_for('upload_check') }}" class="btn btn-outline-primary">
                <i class="fas fa-plus me-2"></i>Analyze Another Check
            </a>
        </div>
    </div>
</div>

<!-- Main Result Alert -->
<div class="row mb-4">
    <div class="col-12">
        {% if result.is_fraud %}
        <div class="alert fraud-alert" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-triangle fa-2x text-danger me-3"></i>
                <div>
                    <h4 class="alert-heading mb-1">⚠️ FRAUD DETECTED</h4>
                    <p class="mb-0">This check has been flagged as potentially fraudulent. Manual review is recommended.</p>
                </div>
            </div>
        </div>
        {% else %}
        <div class="alert safe-alert" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-check-circle fa-2x text-success me-3"></i>
                <div>
                    <h4 class="alert-heading mb-1">✅ NO FRAUD DETECTED</h4>
                    <p class="mb-0">This check appears to be legitimate based on our analysis.</p>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Risk Score and Confidence -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title">Risk Score</h5>
                <div class="risk-score {{ 'risk-high' if result.risk_score > 0.7 else 'risk-medium' if result.risk_score > 0.3 else 'risk-low' }}">
                    {{ "{:.1%}".format(result.risk_score) }}
                </div>
                <div class="progress mt-2">
                    <div class="progress-bar {{ 'bg-danger' if result.risk_score > 0.7 else 'bg-warning' if result.risk_score > 0.3 else 'bg-success' }}" 
                         style="width: {{ result.risk_score * 100 }}%"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title">Confidence Level</h5>
                <div class="risk-score {{ 'risk-high' if result.confidence_score > 0.7 else 'risk-medium' if result.confidence_score > 0.3 else 'risk-low' }}">
                    {{ "{:.1%}".format(result.confidence_score) }}
                </div>
                <div class="progress mt-2">
                    <div class="progress-bar {{ 'bg-success' if result.confidence_score > 0.7 else 'bg-warning' if result.confidence_score > 0.3 else 'bg-danger' }}" 
                         style="width: {{ result.confidence_score * 100 }}%"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Analysis Details -->
<div class="row">
    <div class="col-lg-8">
        <!-- Extracted Elements -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-eye me-2"></i>Extracted Check Elements</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Payee:</strong></td>
                                <td>{{ result.extracted_elements.payee or 'Not found' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Amount (Digits):</strong></td>
                                <td>
                                    {% if result.extracted_elements.amount_digits %}
                                        ${{ "{:,.2f}".format(result.extracted_elements.amount_digits) }}
                                    {% else %}
                                        Not found
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Amount (Words):</strong></td>
                                <td>{{ result.extracted_elements.amount_words or 'Not found' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Date:</strong></td>
                                <td>{{ result.extracted_elements.date or 'Not found' }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Check Number:</strong></td>
                                <td>{{ result.extracted_elements.check_number or 'Not found' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Account Number:</strong></td>
                                <td>{{ result.extracted_elements.account_number or 'Not found' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Bank Name:</strong></td>
                                <td>{{ result.extracted_elements.bank_name or 'Not found' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Signature Present:</strong></td>
                                <td>
                                    {% if result.extracted_elements.signature_present %}
                                        <span class="text-success"><i class="fas fa-check"></i> Yes</span>
                                    {% else %}
                                        <span class="text-danger"><i class="fas fa-times"></i> No</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Extraction Confidence: {{ "{:.1%}".format(result.extracted_elements.confidence_score) }}
                    </small>
                </div>
            </div>
        </div>

        <!-- Fraud Reasons -->
        {% if result.fraud_reasons %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Fraud Indicators</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for reason in result.fraud_reasons %}
                    <div class="col-md-6 mb-2">
                        <div class="alert alert-warning py-2 mb-2">
                            <i class="fas fa-warning me-2"></i>
                            {{ reason.replace('_', ' ').title() }}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Behavioral Analysis -->
        {% if result.behavioral_analysis %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-brain me-2"></i>Behavioral Analysis</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Behavioral Risk Score</h6>
                        <div class="progress mb-3">
                            <div class="progress-bar {{ 'bg-danger' if result.behavioral_analysis.behavioral_score > 0.7 else 'bg-warning' if result.behavioral_analysis.behavioral_score > 0.3 else 'bg-success' }}" 
                                 style="width: {{ result.behavioral_analysis.behavioral_score * 100 }}%">
                                {{ "{:.1%}".format(result.behavioral_analysis.behavioral_score) }}
                            </div>
                        </div>
                    </div>
                </div>
                
                {% if result.behavioral_analysis.get('amount_anomalies') or result.behavioral_analysis.get('payee_anomalies') or result.behavioral_analysis.get('frequency_anomalies') or result.behavioral_analysis.get('timing_anomalies') %}
                <h6>Detected Anomalies</h6>
                <ul class="list-unstyled">
                    {% for key, anomalies in result.behavioral_analysis.items() %}
                        {% if 'anomalies' in key and anomalies %}
                            {% for anomaly in anomalies %}
                            <li><i class="fas fa-exclamation-circle text-warning me-2"></i>{{ anomaly.replace('_', ' ').title() }}</li>
                            {% endfor %}
                        {% endif %}
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>

    <div class="col-lg-4">
        <!-- Customer Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-user me-2"></i>Customer Information</h6>
            </div>
            <div class="card-body">
                <p><strong>Customer ID:</strong> {{ customer_id }}</p>
                <p><strong>Analysis Time:</strong> {{ result.analysis_timestamp[:19].replace('T', ' ') }}</p>
            </div>
        </div>

        <!-- Recommendations -->
        {% if result.recommendations %}
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Recommendations</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    {% for recommendation in result.recommendations %}
                    <li class="mb-2">
                        <i class="fas fa-arrow-right text-primary me-2"></i>
                        {{ recommendation }}
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
        {% endif %}

        <!-- Actions -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="downloadResults()">
                        <i class="fas fa-download me-2"></i>Download Report
                    </button>
                    <button class="btn btn-outline-info" onclick="copyResults()">
                        <i class="fas fa-copy me-2"></i>Copy Results
                    </button>
                    <a href="{{ url_for('upload_check') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Analyze Another Check
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden data for JavaScript -->
<script type="application/json" id="resultData">{{ result | tojson }}</script>
{% endblock %}

{% block scripts %}
<script>
function downloadResults() {
    const resultData = JSON.parse(document.getElementById('resultData').textContent);
    const blob = new Blob([JSON.stringify(resultData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `fraud_analysis_{{ customer_id }}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

function copyResults() {
    const resultData = JSON.parse(document.getElementById('resultData').textContent);
    const text = JSON.stringify(resultData, null, 2);
    navigator.clipboard.writeText(text).then(() => {
        alert('Results copied to clipboard!');
    }).catch(err => {
        console.error('Failed to copy: ', err);
        alert('Failed to copy results to clipboard');
    });
}
</script>
{% endblock %}
