#!/usr/bin/env python3
"""
Flask web application for check fraud detection.
"""
import os
import sys
import json
import uuid
from datetime import datetime
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from werkzeug.utils import secure_filename
import pandas as pd

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.fraud_detector import FraudDetector
from src.data_generator import SyntheticDataGenerator
from src.utils import validate_check_image, check_ollama_connection, load_transaction_data
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set Google Vision API key as environment variable
os.environ['GOOGLE_VISION_API_KEY'] = 'AIzaSyCNPVl987CFH9T1oeRtCskrds_5kZGDQA0'

# Flask app configuration
app = Flask(__name__)
app.secret_key = 'fraud_detection_secret_key_2024'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Configuration
UPLOAD_FOLDER = 'web_uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff'}
TRANSACTION_DATA_FILE = 'sample_data/synthetic_transactions.csv'

# Ensure upload directory exists
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs('sample_data', exist_ok=True)

# Global variables
fraud_detector = None
transaction_data = None


def allowed_file(filename):
    """Check if file extension is allowed."""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def initialize_system():
    """Initialize the fraud detection system."""
    global fraud_detector, transaction_data

    try:
        # Check if transaction data exists, if not generate it
        if not os.path.exists(TRANSACTION_DATA_FILE):
            logger.info("Generating synthetic transaction data...")
            generator = SyntheticDataGenerator(seed=42)
            customers = generator.generate_customers(50)
            transactions_df = generator.generate_transactions(customers, 2000)
            generator.save_to_csv(transactions_df, TRANSACTION_DATA_FILE)
            logger.info(f"Generated {len(transactions_df)} transactions")

        # Load transaction data
        transaction_data = load_transaction_data(TRANSACTION_DATA_FILE)
        logger.info(f"Loaded {len(transaction_data)} transactions")

        # Initialize fraud detector
        fraud_detector = FraudDetector(transaction_data)
        logger.info("Fraud detection system initialized successfully")

        return True
    except Exception as e:
        logger.error(f"Failed to initialize system: {e}")
        # Try to initialize without transaction data
        try:
            fraud_detector = FraudDetector(None)
            logger.info("Fraud detection system initialized without transaction data")
            return True
        except Exception as e2:
            logger.error(f"Failed to initialize even basic system: {e2}")
            return False


@app.route('/')
def index():
    """Main page."""
    # Check system status
    ollama_status = check_ollama_connection()
    system_initialized = fraud_detector is not None

    # Get some statistics
    stats = {}
    if transaction_data is not None:
        stats = {
            'total_transactions': len(transaction_data),
            'total_customers': transaction_data['customer_id'].nunique(),
            'fraud_rate': transaction_data['is_fraud'].mean() if 'is_fraud' in transaction_data.columns else 0
        }

    return render_template('index.html',
                         ollama_status=ollama_status,
                         system_initialized=system_initialized,
                         stats=stats)


@app.route('/upload', methods=['GET', 'POST'])
def upload_check():
    """Upload and analyze check."""
    if request.method == 'POST':
        # Check if file was uploaded
        if 'check_image' not in request.files:
            flash('No file selected', 'error')
            return redirect(request.url)

        file = request.files['check_image']
        customer_id = request.form.get('customer_id', '').strip()
        extraction_mode = request.form.get('extraction_mode', 'vision').strip()
        ocr_engine = request.form.get('ocr_engine', 'tesseract').strip()

        if file.filename == '':
            flash('No file selected', 'error')
            return redirect(request.url)

        # Make customer ID optional - use "UNKNOWN" if not provided
        if not customer_id:
            customer_id = "UNKNOWN_CUSTOMER"

        if file and allowed_file(file.filename):
            # Save uploaded file
            filename = secure_filename(file.filename)
            unique_filename = f"{uuid.uuid4()}_{filename}"
            file_path = os.path.join(UPLOAD_FOLDER, unique_filename)
            file.save(file_path)

            try:
                # Validate image
                if not validate_check_image(file_path):
                    flash('Invalid image file', 'error')
                    os.remove(file_path)
                    return redirect(request.url)

                # Analyze check
                if fraud_detector is None:
                    flash('Fraud detection system not initialized', 'error')
                    os.remove(file_path)
                    return redirect(request.url)

                # Set extraction mode if OCR is selected
                if extraction_mode == 'ocr':
                    fraud_detector.check_extractor.set_extraction_mode('ocr', ocr_engine)
                else:
                    fraud_detector.check_extractor.set_extraction_mode('vision')

                result = fraud_detector.detect_fraud(file_path, customer_id)

                # Convert result to dict for JSON serialization
                result_dict = result.to_dict()
                result_dict['image_filename'] = unique_filename
                result_dict['analysis_timestamp'] = datetime.now().isoformat()

                # Clean up uploaded file
                os.remove(file_path)

                return render_template('results.html',
                                     result=result_dict,
                                     customer_id=customer_id)

            except Exception as e:
                logger.error(f"Error analyzing check: {e}")
                flash(f'Error analyzing check: {str(e)}', 'error')
                if os.path.exists(file_path):
                    os.remove(file_path)
                return redirect(request.url)
        else:
            flash('Invalid file type. Please upload an image file.', 'error')
            return redirect(request.url)

    # GET request - show upload form
    customers = []
    if transaction_data is not None:
        customers = sorted(transaction_data['customer_id'].unique())[:20]  # Show first 20 customers

    return render_template('upload.html', customers=customers)


@app.route('/api/analyze', methods=['POST'])
def api_analyze():
    """API endpoint for check analysis."""
    try:
        if 'check_image' not in request.files:
            return jsonify({'error': 'No file uploaded'}), 400

        file = request.files['check_image']
        customer_id = request.form.get('customer_id', '').strip()

        # Make customer ID optional - use "UNKNOWN" if not provided
        if not customer_id:
            customer_id = "UNKNOWN_CUSTOMER"

        if file.filename == '' or not allowed_file(file.filename):
            return jsonify({'error': 'Invalid file'}), 400

        # Save and analyze
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4()}_{filename}"
        file_path = os.path.join(UPLOAD_FOLDER, unique_filename)
        file.save(file_path)

        try:
            if not validate_check_image(file_path):
                return jsonify({'error': 'Invalid image file'}), 400

            if fraud_detector is None:
                return jsonify({'error': 'System not initialized'}), 500

            result = fraud_detector.detect_fraud(file_path, customer_id)
            result_dict = result.to_dict()
            result_dict['analysis_timestamp'] = datetime.now().isoformat()

            return jsonify(result_dict)

        finally:
            if os.path.exists(file_path):
                os.remove(file_path)

    except Exception as e:
        logger.error(f"API error: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/status')
def system_status():
    """System status endpoint."""
    status = {
        'ollama_connected': check_ollama_connection(),
        'fraud_detector_initialized': fraud_detector is not None,
        'transaction_data_loaded': transaction_data is not None,
        'timestamp': datetime.now().isoformat()
    }

    if transaction_data is not None:
        status['transaction_stats'] = {
            'total_transactions': len(transaction_data),
            'total_customers': transaction_data['customer_id'].nunique(),
            'date_range': {
                'start': transaction_data['date'].min().isoformat(),
                'end': transaction_data['date'].max().isoformat()
            }
        }

    return jsonify(status)


@app.route('/api/test_extraction_modes', methods=['POST'])
def test_extraction_modes():
    """Test both extraction modes on an uploaded image."""
    try:
        if 'check_image' not in request.files:
            return jsonify({'error': 'No file uploaded'}), 400

        file = request.files['check_image']
        if file.filename == '' or not allowed_file(file.filename):
            return jsonify({'error': 'Invalid file'}), 400

        # Save and test
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4()}_{filename}"
        file_path = os.path.join(UPLOAD_FOLDER, unique_filename)
        file.save(file_path)

        try:
            if not validate_check_image(file_path):
                return jsonify({'error': 'Invalid image file'}), 400

            if fraud_detector is None:
                return jsonify({'error': 'System not initialized'}), 500

            # Test both modes
            results = fraud_detector.check_extractor.test_extraction_modes(file_path)

            return jsonify({
                'results': results,
                'available_modes': fraud_detector.check_extractor.get_available_modes()
            })

        finally:
            if os.path.exists(file_path):
                os.remove(file_path)

    except Exception as e:
        logger.error(f"Extraction mode test error: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/generate_data')
def generate_data():
    """Generate new synthetic data."""
    try:
        generator = SyntheticDataGenerator()
        customers = generator.generate_customers(50)
        transactions_df = generator.generate_transactions(customers, 2000)
        generator.save_to_csv(transactions_df, TRANSACTION_DATA_FILE)

        # Reload data
        global transaction_data, fraud_detector
        transaction_data = load_transaction_data(TRANSACTION_DATA_FILE)
        fraud_detector = FraudDetector(transaction_data)

        flash(f'Generated {len(transactions_df)} new transactions', 'success')

    except Exception as e:
        flash(f'Error generating data: {str(e)}', 'error')

    return redirect(url_for('index'))


@app.errorhandler(413)
def too_large(e):
    """Handle file too large error."""
    flash('File too large. Maximum size is 16MB.', 'error')
    return redirect(url_for('upload_check'))


@app.errorhandler(404)
def not_found(e):
    """Handle 404 errors."""
    return render_template('404.html'), 404


@app.errorhandler(500)
def server_error(e):
    """Handle 500 errors."""
    return render_template('500.html'), 500


# Initialize system when module is loaded (not just when run as main)
print("Initializing fraud detection system...")
if initialize_system():
    print("✅ System initialized successfully")
    if transaction_data is not None:
        print(f"✅ Loaded {len(transaction_data)} transactions")
else:
    print("⚠️ System initialization had issues, but continuing...")

if __name__ == '__main__':
    print("=" * 60)
    print("CHECK FRAUD DETECTION WEB APPLICATION")
    print("=" * 60)

    # Check Ollama
    if check_ollama_connection():
        print("✅ Ollama connection successful")
    else:
        print("⚠️  Ollama not available - limited functionality")

    print("\n🌐 Starting web server...")
    print("📍 Open your browser and go to: http://localhost:5001")
    print("=" * 60)

    app.run(debug=True, host='0.0.0.0', port=5001)
