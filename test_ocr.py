#!/usr/bin/env python3
"""
Test OCR functionality with available engines.
"""
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.ocr_extractor import OCRExtractor

def test_ocr_engines():
    """Test available OCR engines."""
    print("Testing OCR Engines (Including Llama OCR for Handwriting)...")

    # Initialize OCR extractor
    extractor = OCRExtractor()

    print(f"Available engines: {extractor.get_available_engines()}")

    # Test with a demo check if available
    demo_check = "sample_data/demo_checks/legitimate_check_1.jpg"

    if os.path.exists(demo_check):
        print(f"\nTesting with demo check: {demo_check}")

        for engine in extractor.get_available_engines():
            print(f"\n--- Testing {engine} ---")
            try:
                result = extractor.test_engine(engine, demo_check)
                if result['success']:
                    print(f"✅ {engine}: Extracted {result['text_length']} characters")
                    print(f"Preview: {result['preview'][:150]}...")

                    # Special note for Llama OCR
                    if engine == "llama_ocr":
                        print("   🎯 Llama OCR is optimized for handwritten text!")
                else:
                    print(f"❌ {engine}: {result['error']}")
            except Exception as e:
                print(f"❌ {engine}: Exception - {e}")

        # Test handwriting-specific recommendation
        print(f"\n🖋️  HANDWRITING RECOMMENDATION:")
        print(f"   For handwritten checks, try engines in this order:")
        print(f"   1. google_vision (Professional-grade, best accuracy)")
        print(f"   2. llama_ocr (AI-powered, excellent for handwriting)")
        print(f"   3. easyocr (Deep learning, good for handwriting)")
        print(f"   4. tesseract (Enhanced with multiple configs)")

        # Test Google Vision API specifically
        if "google_vision" in extractor.get_available_engines():
            print(f"\n🌟 GOOGLE VISION API AVAILABLE:")
            print(f"   - Professional-grade OCR service")
            print(f"   - Excellent for both handwritten and printed text")
            print(f"   - Best overall accuracy expected")
    else:
        print(f"Demo check not found: {demo_check}")
        print("Run 'python create_demo_checks.py' first")

if __name__ == "__main__":
    test_ocr_engines()
