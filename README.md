# Check Fraud Detection System

An AI-powered check fraud detection system using Vision Language Models (VLM) through Ollama for check element extraction and multi-criteria fraud analysis.

## Features

- **Vision LLM Integration**: Uses Ollama with vision models (like LLaVA) to extract check elements
- **Multi-Criteria Fraud Detection**: 
  - Amount mismatch detection (written vs numeric)
  - Date validation
  - Duplicate check detection
  - Behavioral analysis
  - Signature verification
  - Excessive amount detection
- **Synthetic Data Generation**: Creates realistic transaction datasets for testing
- **Behavioral Analysis**: Analyzes customer spending patterns to detect anomalies
- **Comprehensive Reporting**: Detailed fraud analysis reports with recommendations

## Installation

1. **Install Ollama** (if not already installed):
   ```bash
   # macOS
   brew install ollama
   
   # Or download from https://ollama.ai
   ```

2. **Pull the vision model**:
   ```bash
   ollama pull llava:latest
   ```

3. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

## Quick Start

1. **Check Ollama connection**:
   ```bash
   python main.py check-ollama
   ```

2. **Generate synthetic transaction data**:
   ```bash
   python main.py generate-data --customers 50 --transactions 5000
   ```

3. **Analyze a single check**:
   ```bash
   python main.py analyze path/to/check_image.jpg CUST_000001 --data sample_data/synthetic_transactions.csv
   ```

## Usage Examples

### Single Check Analysis

```bash
# Analyze a check image for a specific customer
python main.py analyze check_sample.jpg CUST_000001 --data sample_data/synthetic_transactions.csv
```

### Batch Analysis

```bash
# Analyze multiple checks
python main.py batch images/ customer_mapping.csv --data sample_data/synthetic_transactions.csv
```

The `customer_mapping.csv` should have columns: `image_file`, `customer_id`

### Generate Test Data

```bash
# Generate synthetic transaction data
python main.py generate-data --customers 100 --transactions 10000
```

## API Usage

```python
from src.fraud_detector import FraudDetector
from src.data_generator import SyntheticDataGenerator
import pandas as pd

# Generate sample data
generator = SyntheticDataGenerator()
customers = generator.generate_customers(50)
transactions_df = generator.generate_transactions(customers, 5000)

# Initialize fraud detector
detector = FraudDetector(transactions_df)

# Analyze a check
result = detector.detect_fraud("check_image.jpg", "CUST_000001")

print(f"Fraud detected: {result.is_fraud}")
print(f"Risk score: {result.risk_score:.2f}")
print(f"Confidence: {result.confidence_score:.2f}")
```

## System Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Check Image   │───▶│  Vision LLM      │───▶│ Extracted       │
│                 │    │  (Ollama/LLaVA)  │    │ Elements        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Transaction     │───▶│  Fraud Detector  │◀───│ Multi-Criteria  │
│ History         │    │                  │    │ Analysis        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │ Fraud Detection │
                       │ Result          │
                       └─────────────────┘
```

## Fraud Detection Criteria

1. **Amount Mismatch**: Compares written and numeric amounts
2. **Date Validation**: Checks for future dates or very old dates
3. **Duplicate Detection**: Identifies reused check numbers
4. **Behavioral Analysis**: Analyzes against customer's normal patterns
5. **Signature Verification**: Checks for signature presence
6. **Excessive Amounts**: Detects unusually high amounts

## Configuration

The system uses configurable thresholds in `src/fraud_detector.py`:

```python
self.thresholds = {
    'amount_mismatch_tolerance': 0.01,  # 1% tolerance
    'behavioral_risk_threshold': 0.6,   # Risk score threshold
    'confidence_threshold': 0.7,        # Minimum extraction confidence
    'max_amount_multiplier': 10,        # Max vs average amount
    'duplicate_check_window_days': 30   # Duplicate check window
}
```

## Testing

Run the test suite:

```bash
# Run all tests
pytest tests/

# Run specific test file
pytest tests/test_fraud_detector.py

# Run with coverage
pytest tests/ --cov=src --cov-report=html
```

## Sample Check Format

The system is designed to work with various check formats. Here's an example of the information extracted:

- **Payee**: Person/entity receiving payment
- **Amount (digits)**: Numerical amount (e.g., 88,00,000)
- **Amount (words)**: Written amount (e.g., "Eighty Eight Lakhs")
- **Date**: Check date
- **Account Number**: Bank account number
- **Check Number**: Unique check identifier
- **Signature**: Presence of signature
- **Bank Name**: Issuing bank

## Output

The system generates:

1. **JSON Results**: Machine-readable fraud analysis
2. **Human-Readable Reports**: Detailed analysis with recommendations
3. **Batch Statistics**: Summary statistics for multiple checks

## Limitations

- Requires Ollama with a vision model (LLaVA recommended)
- OCR accuracy depends on image quality
- Written amount parsing is simplified (production would need more robust NLP)
- Signature analysis is basic (presence detection only)

## Future Enhancements

- Advanced signature comparison using neural networks
- Blockchain integration for check traceability
- Physical paper and ink analysis
- Biometric verification integration
- Real-time fraud scoring API

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is for educational and demonstration purposes.
