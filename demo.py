#!/usr/bin/env python3
"""
Demo script for the check fraud detection system.
"""
import sys
import os
from PIL import Image, ImageDraw, ImageFont

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.fraud_detector import FraudDetector
from src.data_generator import SyntheticDataGenerator
from src.utils import setup_directories, check_ollama_connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_sample_check_image(filename="sample_check.jpg"):
    """Create a sample check image for testing."""
    # Create a check-like image
    width, height = 800, 400
    img = Image.new('RGB', (width, height), color='lightblue')
    draw = ImageDraw.Draw(img)
    
    # Try to use a default font, fallback to basic if not available
    try:
        font = ImageFont.truetype("Arial.ttf", 16)
        large_font = ImageFont.truetype("Arial.ttf", 20)
    except:
        font = ImageFont.load_default()
        large_font = font
    
    # Draw check elements
    draw.rectangle([10, 10, width-10, height-10], outline='black', width=2)
    
    # Bank name
    draw.text((20, 20), "DEMO BANK", fill='black', font=large_font)
    
    # Date
    draw.text((600, 50), "Date: 15/03/2024", fill='black', font=font)
    
    # Pay to
    draw.text((20, 80), "Pay to the order of:", fill='black', font=font)
    draw.text((200, 80), "John Doe", fill='black', font=large_font)
    
    # Amount in words
    draw.text((20, 120), "Amount: One Thousand Five Hundred Dollars", fill='black', font=font)
    
    # Amount in digits
    draw.rectangle([600, 110, 750, 140], outline='black')
    draw.text((610, 120), "$1,500.00", fill='black', font=large_font)
    
    # Account number
    draw.text((20, 300), "Account: **********", fill='black', font=font)
    
    # Check number
    draw.text((600, 300), "Check #: 1001", fill='black', font=font)
    
    # Signature line
    draw.line([400, 250, 700, 250], fill='black', width=1)
    draw.text((400, 260), "Signature", fill='gray', font=font)
    
    # Add a simple signature
    draw.text((450, 230), "J. Doe", fill='blue', font=large_font)
    
    img.save(filename)
    logger.info(f"Created sample check image: {filename}")
    return filename


def run_demo():
    """Run a complete demo of the fraud detection system."""
    print("=" * 60)
    print("CHECK FRAUD DETECTION SYSTEM DEMO")
    print("=" * 60)
    
    # Setup directories
    setup_directories()
    
    # Check Ollama connection
    print("\n1. Checking Ollama connection...")
    if not check_ollama_connection():
        print("❌ Ollama not available. This demo will run with limited functionality.")
        print("To get full functionality:")
        print("  1. Install Ollama: https://ollama.ai")
        print("  2. Pull a vision model: ollama pull llava:latest")
        use_ollama = False
    else:
        print("✅ Ollama connection successful!")
        use_ollama = True
    
    # Generate synthetic data
    print("\n2. Generating synthetic transaction data...")
    generator = SyntheticDataGenerator(seed=42)
    customers = generator.generate_customers(20)
    transactions_df = generator.generate_transactions(customers, 1000, fraud_rate=0.1)
    
    # Save transaction data
    data_file = generator.save_to_csv(transactions_df, "sample_data/demo_transactions.csv")
    print(f"✅ Generated {len(transactions_df)} transactions")
    print(f"   - Customers: {len(customers)}")
    print(f"   - Fraud rate: {transactions_df['is_fraud'].mean():.1%}")
    
    # Create sample check image
    print("\n3. Creating sample check image...")
    check_image = create_sample_check_image("sample_data/demo_check.jpg")
    print(f"✅ Created sample check: {check_image}")
    
    # Initialize fraud detector
    print("\n4. Initializing fraud detection system...")
    detector = FraudDetector(transactions_df, model_name="llava:latest")
    print("✅ Fraud detector initialized")
    
    # Analyze the sample check
    print("\n5. Analyzing sample check...")
    customer_id = customers[0].customer_id
    
    if use_ollama:
        try:
            result = detector.detect_fraud(check_image, customer_id)
            
            print(f"✅ Analysis complete!")
            print(f"   - Fraud detected: {'YES' if result.is_fraud else 'NO'}")
            print(f"   - Risk score: {result.risk_score:.2f}/1.00")
            print(f"   - Confidence: {result.confidence_score:.2f}/1.00")
            
            if result.fraud_reasons:
                print(f"   - Fraud reasons: {', '.join([r.value for r in result.fraud_reasons])}")
            
            # Show extracted elements
            elements = result.extracted_elements
            print(f"\n   Extracted Elements:")
            print(f"   - Payee: {elements.payee}")
            print(f"   - Amount (digits): ${elements.amount_digits}" if elements.amount_digits else "   - Amount (digits): Not found")
            print(f"   - Amount (words): {elements.amount_words}")
            print(f"   - Date: {elements.date}")
            print(f"   - Check number: {elements.check_number}")
            print(f"   - Signature present: {'Yes' if elements.signature_present else 'No'}")
            
        except Exception as e:
            print(f"❌ Error during analysis: {e}")
            print("This might be due to Ollama model issues or image processing errors.")
    else:
        print("⚠️  Skipping vision analysis (Ollama not available)")
        print("   The system would normally extract check elements using the vision model.")
    
    # Demonstrate behavioral analysis
    print("\n6. Demonstrating behavioral analysis...")
    customer_profile = detector.behavioral_analyzer.customer_profiles.get(customer_id)
    if customer_profile:
        print(f"✅ Customer profile found:")
        print(f"   - Average transaction: ${customer_profile.avg_transaction_amount:.2f}")
        print(f"   - Max transaction: ${customer_profile.max_transaction_amount:.2f}")
        print(f"   - Common payees: {', '.join(customer_profile.common_payees[:3])}")
        print(f"   - Transaction frequency: {customer_profile.transaction_frequency:.1f}/day")
    
    # Show sample fraud scenarios
    print("\n7. Sample fraud detection scenarios:")
    
    # Scenario 1: Excessive amount
    print("\n   Scenario 1: Excessive Amount")
    from src.models import CheckElements
    excessive_elements = CheckElements(
        payee="Suspicious Payee",
        amount_digits=50000.00,  # Very high amount
        amount_words="Fifty Thousand Dollars",
        date="15/03/2024",
        check_number="9999",
        signature_present=True,
        confidence_score=0.9
    )
    
    # Mock the extractor for this test
    original_extractor = detector.check_extractor.extract_elements
    detector.check_extractor.extract_elements = lambda x: excessive_elements
    
    try:
        result = detector.detect_fraud("dummy_path", customer_id)
        print(f"   - Fraud detected: {'YES' if result.is_fraud else 'NO'}")
        print(f"   - Risk score: {result.risk_score:.2f}")
        if result.fraud_reasons:
            print(f"   - Reasons: {', '.join([r.value for r in result.fraud_reasons])}")
    except Exception as e:
        print(f"   - Error: {e}")
    
    # Restore original extractor
    detector.check_extractor.extract_elements = original_extractor
    
    print("\n" + "=" * 60)
    print("DEMO COMPLETE")
    print("=" * 60)
    print("\nNext steps:")
    print("1. Try analyzing real check images with: python main.py analyze <image_path> <customer_id>")
    print("2. Generate more data with: python main.py generate-data")
    print("3. Run tests with: pytest tests/")
    print("4. Check the generated files in sample_data/ and output/ directories")


if __name__ == "__main__":
    run_demo()
