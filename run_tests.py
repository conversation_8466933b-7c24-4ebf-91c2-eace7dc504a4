#!/usr/bin/env python3
"""
Simple test runner for the check fraud detection system.
This script runs basic tests without requiring pytest.
"""
import sys
import os
import traceback

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test that all modules can be imported."""
    print("Testing imports...")
    
    try:
        from src.models import CheckElements, FraudDetectionResult, CustomerProfile
        print("✅ Models import successful")
    except Exception as e:
        print(f"❌ Models import failed: {e}")
        return False
    
    try:
        from src.data_generator import SyntheticDataGenerator
        print("✅ Data generator import successful")
    except Exception as e:
        print(f"❌ Data generator import failed: {e}")
        return False
    
    try:
        from src.behavioral_analyzer import BehavioralAnalyzer
        print("✅ Behavioral analyzer import successful")
    except Exception as e:
        print(f"❌ Behavioral analyzer import failed: {e}")
        return False
    
    try:
        from src.check_extractor import CheckExtractor
        print("✅ Check extractor import successful")
    except Exception as e:
        print(f"❌ Check extractor import failed: {e}")
        return False
    
    try:
        from src.fraud_detector import FraudDetector
        print("✅ Fraud detector import successful")
    except Exception as e:
        print(f"❌ Fraud detector import failed: {e}")
        return False
    
    try:
        from src.utils import setup_directories, validate_check_image
        print("✅ Utils import successful")
    except Exception as e:
        print(f"❌ Utils import failed: {e}")
        return False
    
    return True


def test_data_generation():
    """Test synthetic data generation."""
    print("\nTesting data generation...")
    
    try:
        from src.data_generator import SyntheticDataGenerator
        
        generator = SyntheticDataGenerator(seed=42)
        
        # Test customer generation
        customers = generator.generate_customers(5)
        assert len(customers) == 5
        print("✅ Customer generation successful")
        
        # Test transaction generation
        transactions_df = generator.generate_transactions(customers, 50)
        assert len(transactions_df) == 50
        assert 'is_fraud' in transactions_df.columns
        print("✅ Transaction generation successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Data generation test failed: {e}")
        traceback.print_exc()
        return False


def test_behavioral_analysis():
    """Test behavioral analysis."""
    print("\nTesting behavioral analysis...")
    
    try:
        from src.data_generator import SyntheticDataGenerator
        from src.behavioral_analyzer import BehavioralAnalyzer
        from src.models import Transaction
        from datetime import datetime
        
        # Generate test data
        generator = SyntheticDataGenerator(seed=42)
        customers = generator.generate_customers(3)
        transactions_df = generator.generate_transactions(customers, 30)
        
        # Test behavioral analyzer
        analyzer = BehavioralAnalyzer(transactions_df)
        assert len(analyzer.customer_profiles) > 0
        print("✅ Behavioral analyzer initialization successful")
        
        # Test transaction analysis
        test_transaction = Transaction(
            customer_id=customers[0].customer_id,
            transaction_id="TEST_001",
            amount=100.0,
            payee="Test Payee",
            date=datetime.now(),
            transaction_type="CHECK",
            account_number="**********"
        )
        
        analysis = analyzer.analyze_transaction(test_transaction)
        assert 'behavioral_score' in analysis
        print("✅ Transaction analysis successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Behavioral analysis test failed: {e}")
        traceback.print_exc()
        return False


def test_fraud_detection_logic():
    """Test fraud detection logic (without vision model)."""
    print("\nTesting fraud detection logic...")
    
    try:
        from src.fraud_detector import FraudDetector
        from src.models import CheckElements
        from src.data_generator import SyntheticDataGenerator
        
        # Generate test data
        generator = SyntheticDataGenerator(seed=42)
        customers = generator.generate_customers(3)
        transactions_df = generator.generate_transactions(customers, 30)
        
        # Create fraud detector
        detector = FraudDetector(transactions_df)
        
        # Test amount mismatch detection
        elements = CheckElements(
            amount_words="One Thousand Dollars",
            amount_digits=1000.0
        )
        result = detector._detect_amount_mismatch(elements)
        assert 'is_mismatch' in result
        print("✅ Amount mismatch detection successful")
        
        # Test date validation
        elements = CheckElements(date="15/03/2024")
        result = detector._validate_date(elements)
        assert 'is_valid' in result
        print("✅ Date validation successful")
        
        # Test excessive amount detection
        elements = CheckElements(amount_digits=100.0)
        result = detector._detect_excessive_amount(elements, customers[0].customer_id)
        assert 'is_excessive' in result
        print("✅ Excessive amount detection successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Fraud detection logic test failed: {e}")
        traceback.print_exc()
        return False


def test_models():
    """Test data models."""
    print("\nTesting data models...")
    
    try:
        from src.models import CheckElements, FraudDetectionResult, FraudReason
        
        # Test CheckElements
        elements = CheckElements(
            payee="Test Payee",
            amount_digits=100.0,
            signature_present=True
        )
        assert elements.payee == "Test Payee"
        print("✅ CheckElements model successful")
        
        # Test FraudDetectionResult
        result = FraudDetectionResult(
            is_fraud=True,
            confidence_score=0.8,
            fraud_reasons=[FraudReason.AMOUNT_MISMATCH],
            risk_score=0.7,
            extracted_elements=elements,
            behavioral_analysis={},
            recommendations=["Test recommendation"]
        )
        
        result_dict = result.to_dict()
        assert 'is_fraud' in result_dict
        assert result_dict['is_fraud'] == True
        print("✅ FraudDetectionResult model successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Models test failed: {e}")
        traceback.print_exc()
        return False


def test_utils():
    """Test utility functions."""
    print("\nTesting utility functions...")
    
    try:
        from src.utils import setup_directories, format_currency, calculate_fraud_statistics
        
        # Test directory setup
        setup_directories()
        print("✅ Directory setup successful")
        
        # Test currency formatting
        formatted = format_currency(1234.56)
        assert formatted == "$1,234.56"
        print("✅ Currency formatting successful")
        
        # Test fraud statistics
        test_results = [
            {'is_fraud': True, 'risk_score': 0.8, 'confidence_score': 0.9, 'fraud_reasons': ['test']},
            {'is_fraud': False, 'risk_score': 0.2, 'confidence_score': 0.8, 'fraud_reasons': []}
        ]
        stats = calculate_fraud_statistics(test_results)
        assert 'total_checks_analyzed' in stats
        assert stats['total_checks_analyzed'] == 2
        print("✅ Fraud statistics calculation successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Utils test failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("=" * 60)
    print("RUNNING BASIC TESTS")
    print("=" * 60)
    
    tests = [
        ("Import Tests", test_imports),
        ("Data Generation", test_data_generation),
        ("Behavioral Analysis", test_behavioral_analysis),
        ("Fraud Detection Logic", test_fraud_detection_logic),
        ("Data Models", test_models),
        ("Utility Functions", test_utils)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Passed: {passed}/{total} tests")
    
    if passed == total:
        print("🎉 All tests passed!")
        print("\nThe system is ready to use. Try running:")
        print("  python demo.py")
        print("  python main.py generate-data")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
        
        if passed >= total // 2:
            print("\nCore functionality appears to work. You can still try:")
            print("  python main.py generate-data")
    
    print("=" * 60)
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
