#!/usr/bin/env python3
"""
Startup script for the Check Fraud Detection Web Application.
This script sets up everything needed and starts the web server.
"""
import os
import sys
import subprocess
import webbrowser
import time
from threading import Timer


def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = ['flask', 'pandas', 'numpy', 'pillow']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages


def install_dependencies():
    """Install missing dependencies."""
    print("📦 Installing required dependencies...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def setup_directories():
    """Create necessary directories."""
    directories = ['sample_data', 'sample_data/demo_checks', 'web_uploads', 'output', 'logs', 'templates']
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    print("✅ Created necessary directories")


def generate_sample_data():
    """Generate sample transaction data if it doesn't exist."""
    data_file = 'sample_data/synthetic_transactions.csv'
    
    if not os.path.exists(data_file):
        print("📊 Generating sample transaction data...")
        try:
            # Add src to path
            sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
            
            from src.data_generator import SyntheticDataGenerator
            
            generator = SyntheticDataGenerator(seed=42)
            customers = generator.generate_customers(50)
            transactions_df = generator.generate_transactions(customers, 2000)
            generator.save_to_csv(transactions_df, data_file)
            
            print(f"✅ Generated {len(transactions_df)} sample transactions")
            return True
        except Exception as e:
            print(f"❌ Failed to generate sample data: {e}")
            return False
    else:
        print("✅ Sample transaction data already exists")
        return True


def create_demo_checks():
    """Create demo check images if they don't exist."""
    demo_dir = 'sample_data/demo_checks'
    
    if not os.path.exists(f'{demo_dir}/legitimate_check_1.jpg'):
        print("🖼️  Creating demo check images...")
        try:
            exec(open('create_demo_checks.py').read())
            print("✅ Demo check images created")
            return True
        except Exception as e:
            print(f"❌ Failed to create demo checks: {e}")
            return False
    else:
        print("✅ Demo check images already exist")
        return True


def check_ollama():
    """Check if Ollama is available."""
    try:
        result = subprocess.run(['ollama', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Ollama is available")
            
            # Check for vision model
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            if 'llava' in result.stdout:
                print("✅ LLaVA vision model is available")
                return True
            else:
                print("⚠️  LLaVA model not found. Run: ollama pull llava:latest")
                return False
        else:
            print("⚠️  Ollama not found")
            return False
    except FileNotFoundError:
        print("⚠️  Ollama not installed")
        return False


def open_browser():
    """Open browser after a delay."""
    time.sleep(3)  # Wait for server to start
    webbrowser.open('http://localhost:5000')


def main():
    """Main setup and startup function."""
    print("=" * 60)
    print("🚀 CHECK FRAUD DETECTION WEB APP STARTUP")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version.split()[0]} detected")
    
    # Setup directories
    setup_directories()
    
    # Check dependencies
    missing = check_dependencies()
    if missing:
        print(f"📦 Missing packages: {', '.join(missing)}")
        if not install_dependencies():
            print("❌ Failed to install dependencies. Please run: pip install -r requirements.txt")
            sys.exit(1)
    else:
        print("✅ All required packages are installed")
    
    # Generate sample data
    if not generate_sample_data():
        print("⚠️  Continuing without sample data")
    
    # Create demo checks
    if not create_demo_checks():
        print("⚠️  Continuing without demo checks")
    
    # Check Ollama
    ollama_available = check_ollama()
    if not ollama_available:
        print("⚠️  Vision analysis will be limited without Ollama")
        print("   Install Ollama from https://ollama.ai")
        print("   Then run: ollama pull llava:latest")
    
    print("\n" + "=" * 60)
    print("🌐 STARTING WEB APPLICATION")
    print("=" * 60)
    
    # Start browser in background
    Timer(3.0, open_browser).start()
    
    print("🔗 Web app will open at: http://localhost:5000")
    print("📁 Demo checks available in: sample_data/demo_checks/")
    print("👤 Test customer IDs: CUST_000001, CUST_000002, CUST_000003")
    print("\n⏳ Starting server...")
    
    # Start the web application
    try:
        import web_app
        # The web_app.py will handle the rest
    except KeyboardInterrupt:
        print("\n\n👋 Shutting down web application...")
        print("Thank you for using Check Fraud Detection System!")
    except Exception as e:
        print(f"\n❌ Error starting web application: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure all dependencies are installed: pip install -r requirements.txt")
        print("2. Check that port 5000 is not in use")
        print("3. Try running directly: python web_app.py")
        sys.exit(1)


if __name__ == "__main__":
    main()
