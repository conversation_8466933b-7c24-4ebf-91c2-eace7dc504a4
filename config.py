"""
Configuration settings for the check fraud detection system.
"""

# Ollama Configuration
OLLAMA_MODEL = "llava:latest"
OLLAMA_TIMEOUT = 60  # seconds

# Fraud Detection Thresholds
FRAUD_THRESHOLDS = {
    'amount_mismatch_tolerance': 0.01,  # 1% tolerance for amount differences
    'behavioral_risk_threshold': 0.6,   # Risk score threshold for behavioral analysis
    'confidence_threshold': 0.7,        # Minimum confidence for extraction
    'max_amount_multiplier': 10,        # Max amount vs customer average multiplier
    'duplicate_check_window_days': 30,  # Days to check for duplicate checks
    'high_amount_threshold': 10000,     # Threshold for high amounts (unknown customers)
    'old_check_days': 180,              # Days after which a check is considered old
    'future_date_tolerance_days': 1     # Days in future allowed for check dates
}

# Data Generation Settings
DATA_GENERATION = {
    'default_customers': 50,
    'default_transactions': 5000,
    'default_fraud_rate': 0.05,  # 5% fraud rate
    'min_transaction_amount': 1.0,
    'max_customer_age_days': 3650,  # 10 years
    'min_customer_age_days': 30
}

# File Paths
PATHS = {
    'sample_data_dir': 'sample_data',
    'output_dir': 'output',
    'logs_dir': 'logs',
    'default_transaction_file': 'sample_data/synthetic_transactions.csv',
    'default_customer_mapping': 'sample_data/customer_mapping_example.csv'
}

# Logging Configuration
LOGGING = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_logging': True,
    'console_logging': True
}

# Vision Model Prompts
VISION_PROMPTS = {
    'check_analysis': """
    Analyze this check image and extract the following information in a structured format:
    
    1. Payee (Pay to the order of): The person/entity receiving payment
    2. Amount in words: The written amount (e.g., "Eighty Eight Lakhs")
    3. Amount in digits: The numerical amount (e.g., 88,00,000)
    4. Date: The date on the check
    5. Account Number: The account number at the bottom
    6. Check Number: The check number (usually top right)
    7. Signature: Whether a signature is present (yes/no)
    8. Bank Name: The name of the bank
    
    Please provide the response in this exact format:
    PAYEE: [extracted payee]
    AMOUNT_WORDS: [amount in words]
    AMOUNT_DIGITS: [numerical amount]
    DATE: [date]
    ACCOUNT_NUMBER: [account number]
    CHECK_NUMBER: [check number]
    SIGNATURE_PRESENT: [yes/no]
    BANK_NAME: [bank name]
    
    If any field cannot be clearly identified, write "NOT_FOUND" for that field.
    """
}

# Transaction Categories and Payees for Data Generation
TRANSACTION_DATA = {
    'categories': [
        'Groceries', 'Gas', 'Restaurant', 'Shopping', 'Utilities',
        'Healthcare', 'Entertainment', 'Travel', 'Education', 'Insurance'
    ],
    
    'payee_types': {
        'Groceries': ['Walmart', 'Target', 'Kroger', 'Safeway', 'Whole Foods'],
        'Gas': ['Shell', 'Exxon', 'BP', 'Chevron', 'Mobil'],
        'Restaurant': ['McDonald\'s', 'Starbucks', 'Subway', 'Pizza Hut', 'KFC'],
        'Shopping': ['Amazon', 'eBay', 'Best Buy', 'Home Depot', 'Macy\'s'],
        'Utilities': ['Electric Company', 'Gas Company', 'Water Department', 'Internet Provider'],
        'Healthcare': ['City Hospital', 'Family Clinic', 'Pharmacy Plus', 'Dental Care'],
        'Entertainment': ['Netflix', 'Spotify', 'Movie Theater', 'Concert Hall'],
        'Travel': ['Airlines', 'Hotel Chain', 'Car Rental', 'Travel Agency'],
        'Education': ['University', 'Online Course', 'Bookstore', 'Training Center'],
        'Insurance': ['Auto Insurance', 'Health Insurance', 'Life Insurance', 'Home Insurance']
    }
}

# Fraud Types for Synthetic Data
FRAUD_TYPES = [
    'excessive_amount',
    'unusual_payee', 
    'frequent_transactions',
    'unusual_time',
    'duplicate_check',
    'amount_mismatch',
    'invalid_date',
    'missing_signature'
]

# Image Processing Settings
IMAGE_PROCESSING = {
    'max_image_size': 50 * 1024 * 1024,  # 50MB
    'min_image_size': 1024,  # 1KB
    'supported_formats': ['.jpg', '.jpeg', '.png', '.bmp', '.tiff'],
    'thumbnail_size': (1024, 1024),
    'image_quality': 85
}

# API Settings (for future use)
API_SETTINGS = {
    'max_requests_per_minute': 60,
    'timeout_seconds': 30,
    'retry_attempts': 3,
    'retry_delay': 1  # seconds
}
