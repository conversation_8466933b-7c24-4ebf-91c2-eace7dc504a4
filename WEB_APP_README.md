# Check Fraud Detection Web Application

A visual web interface for the AI-powered check fraud detection system. Upload check images and get instant fraud analysis results through an intuitive web interface.

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)
```bash
python start_web_app.py
```
This script will:
- Install dependencies
- Generate sample data
- Create demo check images
- Start the web server
- Open your browser automatically

### Option 2: Manual Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Generate sample data
python main.py generate-data

# Create demo checks
python create_demo_checks.py

# Start web server
python web_app.py
```

## 🌐 Web Interface Features

### Dashboard
- **System Status**: Real-time status of Ollama, fraud detection engine, and data
- **Statistics**: Transaction data overview and fraud rates
- **Quick Actions**: Direct access to main features

### Check Analysis
- **Drag & Drop Upload**: Easy file upload with preview
- **Customer Selection**: Choose from existing customers or enter new ID
- **Real-time Analysis**: Instant fraud detection results
- **Visual Results**: Color-coded risk indicators and detailed breakdowns

### Results Display
- **Fraud Status**: Clear indication of fraud detection
- **Risk Score**: Visual risk assessment with progress bars
- **Extracted Elements**: All check information extracted by AI
- **Behavioral Analysis**: Customer pattern analysis
- **Recommendations**: Actionable next steps
- **Export Options**: Download results as JSON

## 📱 Screenshots

### Main Dashboard
- System status indicators
- Transaction statistics
- Quick action buttons

### Upload Interface
- Drag & drop file upload
- Customer ID selection
- Image preview
- Upload progress

### Results Page
- Fraud detection status
- Risk score visualization
- Detailed analysis breakdown
- Recommendations panel

## 🔧 API Endpoints

### Web Routes
- `GET /` - Main dashboard
- `GET /upload` - Upload form
- `POST /upload` - Process check upload
- `GET /status` - System status (JSON)
- `GET /generate_data` - Generate new sample data

### API Routes
- `POST /api/analyze` - Analyze check via API
  ```bash
  curl -X POST -F "check_image=@check.jpg" -F "customer_id=CUST_001" http://localhost:5000/api/analyze
  ```

## 🧪 Testing with Demo Checks

The system includes pre-generated demo check images:

### Legitimate Checks
- `legitimate_check_1.jpg` - Normal check ($1,500)
- `legitimate_check_2.jpg` - Normal check ($750.50)
- `legitimate_check_3.jpg` - Normal check ($300)

### Fraudulent Checks
- `fraudulent_check_1.jpg` - Multiple issues:
  - Amount mismatch (words vs digits)
  - Future date
  - Missing signature
- `fraudulent_check_2.jpg` - Issues:
  - Very old date
  - Duplicate check number

### Test Customer IDs
- `CUST_000001` - Has transaction history
- `CUST_000002` - Has transaction history  
- `CUST_000003` - Has transaction history

## 🎯 How to Test

1. **Start the web app**: `python start_web_app.py`
2. **Open browser**: Go to http://localhost:5000
3. **Upload a demo check**:
   - Click "Analyze Check"
   - Select a customer ID (e.g., CUST_000001)
   - Upload a demo check image
   - View the results

## 🔍 Understanding Results

### Risk Score
- **Green (0-30%)**: Low risk, likely legitimate
- **Yellow (30-70%)**: Medium risk, review recommended
- **Red (70-100%)**: High risk, likely fraudulent

### Fraud Indicators
- **Amount Mismatch**: Written amount doesn't match digits
- **Invalid Date**: Future date or very old date
- **Duplicate Check**: Same check number used recently
- **Unusual Behavior**: Doesn't match customer patterns
- **Suspicious Signature**: Missing or questionable signature
- **Excessive Amount**: Much higher than customer's normal amounts

### Extracted Elements
- **Payee**: Who the check is made out to
- **Amounts**: Both written and numeric amounts
- **Date**: Check date
- **Check Number**: Unique check identifier
- **Account Info**: Bank and account details
- **Signature**: Whether signature is present

## 🛠️ Configuration

### Environment Variables
```bash
export FLASK_ENV=development  # For development mode
export FLASK_DEBUG=1         # Enable debug mode
```

### File Upload Limits
- Maximum file size: 16MB
- Supported formats: JPG, PNG, GIF, BMP, TIFF

### Ollama Configuration
- Default model: `llava:latest`
- Timeout: 60 seconds
- Auto-retry on connection issues

## 🚨 Troubleshooting

### Common Issues

**"Ollama not available"**
```bash
# Install Ollama
brew install ollama  # macOS
# or download from https://ollama.ai

# Pull vision model
ollama pull llava:latest
```

**"Port 5000 already in use"**
```bash
# Kill process using port 5000
lsof -ti:5000 | xargs kill -9

# Or change port in web_app.py
app.run(port=5001)
```

**"Module not found" errors**
```bash
# Install dependencies
pip install -r requirements.txt

# Or install individually
pip install flask pandas numpy pillow
```

**"No transaction data"**
```bash
# Generate sample data
python main.py generate-data
```

### Debug Mode
Enable debug mode for detailed error messages:
```bash
export FLASK_DEBUG=1
python web_app.py
```

## 🔒 Security Considerations

### File Upload Security
- File type validation
- File size limits
- Secure filename handling
- Temporary file cleanup

### Data Privacy
- Uploaded images are deleted after analysis
- No persistent storage of check images
- Transaction data is synthetic (for demo)

## 📊 Performance

### Typical Response Times
- File upload: < 1 second
- Image processing: 2-5 seconds
- Fraud analysis: 1-3 seconds
- Total analysis time: 3-8 seconds

### Resource Usage
- Memory: ~200MB base + ~500MB per analysis
- CPU: Moderate during analysis
- Storage: Minimal (temporary files only)

## 🔄 Updates and Maintenance

### Regenerate Sample Data
```bash
python main.py generate-data --customers 100 --transactions 10000
```

### Update Demo Checks
```bash
python create_demo_checks.py
```

### Clear Uploads
```bash
rm -rf web_uploads/*
```

## 📝 Logs

Application logs are available in:
- Console output (real-time)
- `logs/` directory (if file logging enabled)

## 🤝 Integration

### Embed in Existing Systems
The web app can be integrated into existing systems:
- Use the `/api/analyze` endpoint for programmatic access
- Embed the upload form in other web pages
- Use the fraud detection engine directly

### Custom Styling
Modify `templates/base.html` to match your organization's branding.

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the main README.md
3. Check system status at `/status`
4. Enable debug mode for detailed errors

---

**Ready to detect fraud? Start with:** `python start_web_app.py` 🚀
