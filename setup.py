#!/usr/bin/env python3
"""
Setup script for the Check Fraud Detection System.
"""
import subprocess
import sys
import os


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False


def check_python():
    """Check if Python is available."""
    try:
        version = subprocess.check_output([sys.executable, "--version"], text=True)
        print(f"✅ Python found: {version.strip()}")
        return True
    except Exception as e:
        print(f"❌ Python check failed: {e}")
        return False


def install_dependencies():
    """Install Python dependencies."""
    if not os.path.exists("requirements.txt"):
        print("❌ requirements.txt not found")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing Python dependencies"
    )


def check_ollama():
    """Check if Ollama is available."""
    print("\nChecking Ollama installation...")
    try:
        result = subprocess.run("ollama --version", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Ollama found: {result.stdout.strip()}")
            return True
        else:
            print("❌ Ollama not found")
            return False
    except Exception:
        print("❌ Ollama not found")
        return False


def setup_ollama():
    """Setup Ollama with vision model."""
    if not check_ollama():
        print("\n📋 Ollama Installation Instructions:")
        print("1. Visit https://ollama.ai to download and install Ollama")
        print("2. After installation, run: ollama pull llava:latest")
        print("3. Then re-run this setup script")
        return False
    
    print("\nChecking for vision model...")
    try:
        result = subprocess.run("ollama list", shell=True, capture_output=True, text=True)
        if "llava" in result.stdout:
            print("✅ LLaVA model found")
            return True
        else:
            print("📥 Downloading LLaVA model (this may take a while)...")
            return run_command("ollama pull llava:latest", "Downloading LLaVA model")
    except Exception as e:
        print(f"❌ Error checking Ollama models: {e}")
        return False


def create_directories():
    """Create necessary directories."""
    directories = ["sample_data", "output", "logs"]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    print("✅ Created necessary directories")
    return True


def run_tests():
    """Run the test suite."""
    return run_command(
        f"{sys.executable} -m pytest tests/ -v",
        "Running test suite"
    )


def generate_sample_data():
    """Generate sample data for testing."""
    return run_command(
        f"{sys.executable} main.py generate-data --customers 20 --transactions 1000",
        "Generating sample transaction data"
    )


def main():
    """Main setup function."""
    print("=" * 60)
    print("CHECK FRAUD DETECTION SYSTEM SETUP")
    print("=" * 60)
    
    success_count = 0
    total_steps = 7
    
    # Step 1: Check Python
    if check_python():
        success_count += 1
    
    # Step 2: Create directories
    if create_directories():
        success_count += 1
    
    # Step 3: Install dependencies
    if install_dependencies():
        success_count += 1
    
    # Step 4: Setup Ollama
    if setup_ollama():
        success_count += 1
    
    # Step 5: Run tests
    if run_tests():
        success_count += 1
    
    # Step 6: Generate sample data
    if generate_sample_data():
        success_count += 1
    
    # Step 7: Final check
    print("\n" + "=" * 60)
    print("SETUP SUMMARY")
    print("=" * 60)
    print(f"Completed: {success_count}/{total_steps} steps")
    
    if success_count == total_steps:
        print("🎉 Setup completed successfully!")
        print("\nNext steps:")
        print("1. Run the demo: python demo.py")
        print("2. Analyze a check: python main.py analyze <image_path> <customer_id>")
        print("3. Check the README.md for more information")
        success_count += 1
    else:
        print("⚠️  Setup completed with some issues")
        print("Please check the error messages above and resolve any issues")
        
        if success_count >= 3:  # Core functionality available
            print("\nCore functionality should still work:")
            print("- Data generation: python main.py generate-data")
            print("- Basic fraud detection (without vision): available")
    
    print("\n" + "=" * 60)


if __name__ == "__main__":
    main()
