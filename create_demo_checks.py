#!/usr/bin/env python3
"""
Create demo check images for testing the web interface.
"""
import os
from PIL import Image, ImageDraw, ImageFont
import random
from datetime import datetime, timed<PERSON><PERSON>


def create_check_image(payee, amount_digits, amount_words, date, check_number, 
                      account_number, bank_name, has_signature=True, filename=None):
    """Create a realistic check image."""
    
    # Check dimensions
    width, height = 800, 400
    
    # Create image with light blue background (typical check color)
    img = Image.new('RGB', (width, height), color='#E6F3FF')
    draw = ImageDraw.Draw(img)
    
    # Try to load fonts, fallback to default
    try:
        font_small = ImageFont.truetype("Arial.ttf", 12)
        font_medium = ImageFont.truetype("Arial.ttf", 16)
        font_large = ImageFont.truetype("Arial.ttf", 20)
        font_signature = ImageFont.truetype("Arial.ttf", 18)
    except:
        font_small = ImageFont.load_default()
        font_medium = ImageFont.load_default()
        font_large = ImageFont.load_default()
        font_signature = ImageFont.load_default()
    
    # Draw border
    draw.rectangle([5, 5, width-5, height-5], outline='black', width=2)
    
    # Bank name and logo area
    draw.rectangle([10, 10, 300, 60], outline='navy', width=1)
    draw.text((20, 20), bank_name, fill='navy', font=font_large)
    draw.text((20, 45), "Member FDIC", fill='navy', font=font_small)
    
    # Check number (top right)
    draw.text((width-150, 20), f"Check #: {check_number}", fill='black', font=font_medium)
    
    # Date field
    draw.text((width-200, 60), f"Date: {date}", fill='black', font=font_medium)
    
    # Pay to the order of
    draw.text((20, 100), "Pay to the", fill='black', font=font_small)
    draw.text((20, 120), "order of:", fill='black', font=font_small)
    
    # Payee line
    draw.line([100, 130, 600, 130], fill='black', width=1)
    draw.text((110, 110), payee, fill='black', font=font_large)
    
    # Amount box (right side)
    draw.rectangle([620, 100, 780, 140], outline='black', width=2)
    draw.text((630, 115), f"${amount_digits:,.2f}", fill='black', font=font_large)
    
    # Amount in words
    draw.text((20, 160), "Amount:", fill='black', font=font_small)
    draw.line([80, 180, 700, 180], fill='black', width=1)
    draw.text((90, 160), amount_words, fill='black', font=font_medium)
    
    # Memo line
    draw.text((20, 220), "Memo:", fill='black', font=font_small)
    draw.line([70, 240, 400, 240], fill='black', width=1)
    
    # Signature line
    draw.text((450, 220), "Signature:", fill='black', font=font_small)
    draw.line([520, 240, 750, 240], fill='black', width=1)
    
    # Add signature if specified
    if has_signature:
        # Simple signature simulation
        signature_text = payee.split()[0] if payee else "J. Doe"
        draw.text((530, 215), signature_text, fill='blue', font=font_signature)
    
    # Account information at bottom
    draw.text((20, 320), f"⑈{account_number}⑈ ⑆{check_number}⑆ {random.randint(*********, *********)}", 
              fill='black', font=font_small)
    
    # Bank routing info
    draw.text((20, 350), f"Routing: {random.randint(*********, *********)}", 
              fill='black', font=font_small)
    draw.text((200, 350), f"Account: {account_number}", fill='black', font=font_small)
    
    # Security features (watermark-like)
    draw.text((width//2-50, height//2), "VOID", fill='lightgray', font=font_large)
    
    # Save image
    if filename:
        img.save(filename)
        print(f"Created check image: {filename}")
    
    return img


def create_demo_checks():
    """Create a set of demo check images."""
    
    # Ensure directory exists
    os.makedirs('sample_data/demo_checks', exist_ok=True)
    
    # Demo check data
    checks_data = [
        {
            'payee': 'John Smith',
            'amount_digits': 1500.00,
            'amount_words': 'One Thousand Five Hundred Dollars',
            'date': '03/15/2024',
            'check_number': '1001',
            'account_number': '**********',
            'bank_name': 'First National Bank',
            'has_signature': True,
            'filename': 'sample_data/demo_checks/legitimate_check_1.jpg'
        },
        {
            'payee': 'Jane Doe',
            'amount_digits': 750.50,
            'amount_words': 'Seven Hundred Fifty Dollars and 50/100',
            'date': '03/16/2024',
            'check_number': '1002',
            'account_number': '**********',
            'bank_name': 'First National Bank',
            'has_signature': True,
            'filename': 'sample_data/demo_checks/legitimate_check_2.jpg'
        },
        {
            'payee': 'Suspicious Payee LLC',
            'amount_digits': 50000.00,
            'amount_words': 'Five Hundred Dollars',  # Mismatch!
            'date': '12/25/2025',  # Future date!
            'check_number': '1003',
            'account_number': '**********',
            'bank_name': 'First National Bank',
            'has_signature': False,  # No signature!
            'filename': 'sample_data/demo_checks/fraudulent_check_1.jpg'
        },
        {
            'payee': 'Bob Johnson',
            'amount_digits': 2500.00,
            'amount_words': 'Two Thousand Five Hundred Dollars',
            'date': '01/01/2020',  # Very old date
            'check_number': '1001',  # Duplicate check number!
            'account_number': '**********',
            'bank_name': 'First National Bank',
            'has_signature': True,
            'filename': 'sample_data/demo_checks/fraudulent_check_2.jpg'
        },
        {
            'payee': 'Alice Williams',
            'amount_digits': 300.00,
            'amount_words': 'Three Hundred Dollars',
            'date': '03/17/2024',
            'check_number': '1004',
            'account_number': '**********',
            'bank_name': 'First National Bank',
            'has_signature': True,
            'filename': 'sample_data/demo_checks/legitimate_check_3.jpg'
        }
    ]
    
    print("Creating demo check images...")
    
    for check_data in checks_data:
        create_check_image(**check_data)
    
    print(f"\n✅ Created {len(checks_data)} demo check images in sample_data/demo_checks/")
    print("\nDemo checks created:")
    print("📄 legitimate_check_1.jpg - Normal check")
    print("📄 legitimate_check_2.jpg - Normal check")
    print("📄 legitimate_check_3.jpg - Normal check")
    print("⚠️  fraudulent_check_1.jpg - Amount mismatch, future date, no signature")
    print("⚠️  fraudulent_check_2.jpg - Old date, duplicate check number")
    
    # Create customer mapping file for batch testing
    mapping_data = [
        "image_file,customer_id",
        "legitimate_check_1.jpg,CUST_000001",
        "legitimate_check_2.jpg,CUST_000002", 
        "legitimate_check_3.jpg,CUST_000003",
        "fraudulent_check_1.jpg,CUST_000001",
        "fraudulent_check_2.jpg,CUST_000001"
    ]
    
    with open('sample_data/demo_checks/customer_mapping.csv', 'w') as f:
        f.write('\n'.join(mapping_data))
    
    print("\n📋 Created customer_mapping.csv for batch testing")
    print("\nYou can now:")
    print("1. Start the web app: python web_app.py")
    print("2. Upload these demo checks to test fraud detection")
    print("3. Use customer IDs: CUST_000001, CUST_000002, CUST_000003")


if __name__ == "__main__":
    create_demo_checks()
