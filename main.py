"""
Main application for check fraud detection system.
"""
import argparse
import sys
import os
from datetime import datetime
import pandas as pd

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.fraud_detector import FraudDetector
from src.data_generator import SyntheticDataGenerator
from src.utils import (
    setup_directories, save_results_to_json, validate_check_image,
    create_fraud_report, check_ollama_connection, load_transaction_data
)
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def generate_sample_data(num_customers: int = 50, num_transactions: int = 5000):
    """Generate synthetic transaction data for testing."""
    logger.info("Generating synthetic transaction data...")
    
    generator = SyntheticDataGenerator()
    
    # Generate customers
    customers = generator.generate_customers(num_customers)
    
    # Generate transactions
    transactions_df = generator.generate_transactions(customers, num_transactions)
    
    # Save to CSV
    filename = generator.save_to_csv(transactions_df, "sample_data/synthetic_transactions.csv")
    
    logger.info(f"Generated {num_transactions} transactions for {num_customers} customers")
    logger.info(f"Data saved to: {filename}")
    
    return transactions_df


def analyze_check(image_path: str, customer_id: str, transaction_data_path: str = None):
    """Analyze a single check for fraud."""
    logger.info(f"Analyzing check: {image_path} for customer: {customer_id}")
    
    # Validate inputs
    if not validate_check_image(image_path):
        logger.error("Invalid check image")
        return None
    
    # Load transaction data if provided
    transaction_data = None
    if transaction_data_path and os.path.exists(transaction_data_path):
        transaction_data = load_transaction_data(transaction_data_path)
        logger.info(f"Loaded transaction data with {len(transaction_data)} records")
    
    # Initialize fraud detector
    fraud_detector = FraudDetector(transaction_data)
    
    # Perform fraud detection
    result = fraud_detector.detect_fraud(image_path, customer_id)
    
    # Convert to dictionary for JSON serialization
    result_dict = result.to_dict()
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    json_filename = f"fraud_analysis_{customer_id}_{timestamp}.json"
    save_results_to_json(result_dict, json_filename)
    
    # Create and save report
    report = create_fraud_report(result_dict, customer_id)
    report_filename = f"output/fraud_report_{customer_id}_{timestamp}.txt"
    with open(report_filename, 'w') as f:
        f.write(report)
    
    logger.info(f"Analysis complete. Report saved to: {report_filename}")
    
    # Print summary
    print("\n" + "="*60)
    print("FRAUD DETECTION SUMMARY")
    print("="*60)
    print(f"Customer ID: {customer_id}")
    print(f"Check Image: {image_path}")
    print(f"Fraud Detected: {'YES' if result.is_fraud else 'NO'}")
    print(f"Risk Score: {result.risk_score:.2f}/1.00")
    print(f"Confidence: {result.confidence_score:.2f}/1.00")
    
    if result.fraud_reasons:
        print(f"Fraud Reasons: {', '.join([r.value for r in result.fraud_reasons])}")
    
    print(f"\nDetailed report: {report_filename}")
    print("="*60)
    
    return result_dict


def batch_analyze(image_directory: str, customer_mapping_file: str, 
                 transaction_data_path: str = None):
    """Analyze multiple checks in batch mode."""
    logger.info(f"Starting batch analysis of directory: {image_directory}")
    
    # Load customer mapping
    try:
        customer_mapping = pd.read_csv(customer_mapping_file)
        logger.info(f"Loaded customer mapping with {len(customer_mapping)} entries")
    except Exception as e:
        logger.error(f"Error loading customer mapping: {e}")
        return
    
    # Load transaction data
    transaction_data = None
    if transaction_data_path and os.path.exists(transaction_data_path):
        transaction_data = load_transaction_data(transaction_data_path)
    
    # Initialize fraud detector
    fraud_detector = FraudDetector(transaction_data)
    
    results = []
    
    # Process each image
    for _, row in customer_mapping.iterrows():
        image_file = row['image_file']
        customer_id = row['customer_id']
        
        image_path = os.path.join(image_directory, image_file)
        
        if not validate_check_image(image_path):
            logger.warning(f"Skipping invalid image: {image_path}")
            continue
        
        try:
            result = fraud_detector.detect_fraud(image_path, customer_id)
            result_dict = result.to_dict()
            result_dict['image_file'] = image_file
            results.append(result_dict)
            
            logger.info(f"Processed {image_file}: Fraud={result.is_fraud}, Risk={result.risk_score:.2f}")
            
        except Exception as e:
            logger.error(f"Error processing {image_file}: {e}")
    
    # Save batch results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    batch_filename = f"batch_analysis_{timestamp}.json"
    save_results_to_json(results, batch_filename)
    
    # Generate summary statistics
    from src.utils import calculate_fraud_statistics
    stats = calculate_fraud_statistics(results)
    stats_filename = f"batch_statistics_{timestamp}.json"
    save_results_to_json(stats, stats_filename)
    
    logger.info(f"Batch analysis complete. Results saved to output/{batch_filename}")
    print(f"\nBatch Analysis Summary:")
    print(f"Total checks analyzed: {stats['total_checks_analyzed']}")
    print(f"Fraud detected: {stats['fraud_detected']}")
    print(f"Fraud rate: {stats['fraud_rate']:.2%}")
    print(f"Average risk score: {stats['average_risk_score']:.2f}")


def main():
    """Main application entry point."""
    parser = argparse.ArgumentParser(description="Check Fraud Detection System")
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Generate data command
    gen_parser = subparsers.add_parser('generate-data', help='Generate synthetic transaction data')
    gen_parser.add_argument('--customers', type=int, default=50, help='Number of customers')
    gen_parser.add_argument('--transactions', type=int, default=5000, help='Number of transactions')
    
    # Analyze single check command
    analyze_parser = subparsers.add_parser('analyze', help='Analyze a single check')
    analyze_parser.add_argument('image_path', help='Path to check image')
    analyze_parser.add_argument('customer_id', help='Customer ID')
    analyze_parser.add_argument('--data', help='Path to transaction data CSV')
    
    # Batch analyze command
    batch_parser = subparsers.add_parser('batch', help='Batch analyze multiple checks')
    batch_parser.add_argument('image_directory', help='Directory containing check images')
    batch_parser.add_argument('mapping_file', help='CSV file mapping images to customer IDs')
    batch_parser.add_argument('--data', help='Path to transaction data CSV')
    
    # Check Ollama command
    check_parser = subparsers.add_parser('check-ollama', help='Check Ollama connection')
    check_parser.add_argument('--model', default='llava:latest', help='Model name to check')
    
    args = parser.parse_args()
    
    # Setup directories
    setup_directories()
    
    if args.command == 'generate-data':
        generate_sample_data(args.customers, args.transactions)
    
    elif args.command == 'analyze':
        # Check Ollama connection first
        if not check_ollama_connection():
            print("Error: Cannot connect to Ollama. Please ensure Ollama is running and the model is available.")
            sys.exit(1)
        
        analyze_check(args.image_path, args.customer_id, args.data)
    
    elif args.command == 'batch':
        # Check Ollama connection first
        if not check_ollama_connection():
            print("Error: Cannot connect to Ollama. Please ensure Ollama is running and the model is available.")
            sys.exit(1)
        
        batch_analyze(args.image_directory, args.mapping_file, args.data)
    
    elif args.command == 'check-ollama':
        if check_ollama_connection(args.model):
            print(f"✓ Ollama connection successful. Model '{args.model}' is available.")
        else:
            print(f"✗ Ollama connection failed or model '{args.model}' not available.")
            print("Please ensure Ollama is running and pull the required model:")
            print(f"ollama pull {args.model}")
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
