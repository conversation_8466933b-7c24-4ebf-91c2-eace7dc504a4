#!/usr/bin/env python3
"""
Create reference signature images for testing signature comparison.
"""
import os
from PIL import Image, ImageDraw, ImageFont
import random


def create_signature_image(signature_text, account_number, filename):
    """Create a signature image for the given account."""
    
    # Signature image dimensions
    width, height = 300, 100
    
    # Create image with white background
    img = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(img)
    
    # Try to load fonts, fallback to default
    try:
        font = ImageFont.truetype("Arial.ttf", 24)
    except:
        font = ImageFont.load_default()
    
    # Draw signature text in a cursive style
    x = random.randint(20, 50)
    y = random.randint(30, 50)
    
    # Use blue ink color for signature
    draw.text((x, y), signature_text, fill='blue', font=font)
    
    # Add some random variation to make it look more natural
    # Add a small underline
    text_width = draw.textlength(signature_text, font=font)
    draw.line([x, y + 35, x + text_width, y + 35], fill='blue', width=2)
    
    # Save the signature
    img.save(filename)
    print(f"Created reference signature for account {account_number}: {filename}")


def create_reference_signatures():
    """Create reference signatures for demo accounts."""
    
    # Ensure signature directory exists
    os.makedirs('sig', exist_ok=True)
    
    # Account numbers and corresponding signature texts
    signatures_data = [
        {
            'account_number': '**************',
            'signature_text': 'John Smith',
            'filename': 'sig/**************.png'
        },
        {
            'account_number': '**********',
            'signature_text': 'J. Doe',
            'filename': 'sig/**********.png'
        },
        {
            'account_number': '**********',
            'signature_text': 'Jane Smith',
            'filename': 'sig/**********.png'
        }
    ]
    
    print("Creating reference signature images...")
    
    for sig_data in signatures_data:
        create_signature_image(
            sig_data['signature_text'],
            sig_data['account_number'],
            sig_data['filename']
        )
    
    print(f"\n✅ Created {len(signatures_data)} reference signatures in 'sig/' directory")
    print("\nReference signatures created for accounts:")
    for sig_data in signatures_data:
        print(f"📝 {sig_data['account_number']} - {sig_data['signature_text']}")
    
    print("\nNow you can:")
    print("1. Test signature comparison with these account numbers")
    print("2. Add more reference signatures by placing images in 'sig/' directory")
    print("3. Name signature files with the account number (e.g., '**************.png')")


if __name__ == "__main__":
    create_reference_signatures()
