#!/usr/bin/env python3
"""
Test OCR+LLM pipeline with correct model.
"""
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.ocr_extractor import OCRExtractor

def test_ocr_llm_pipeline():
    """Test the complete OCR+LLM pipeline."""
    print("Testing OCR+LLM Pipeline...")
    
    # Initialize with correct model
    extractor = OCRExtractor(preferred_engine="tesseract", llm_model="gemma3:latest")
    
    print(f"Available engines: {extractor.get_available_engines()}")
    print(f"LLM model: {extractor.llm_model}")
    
    # Test with a demo check if available
    demo_check = "sample_data/demo_checks/legitimate_check_1.jpg"
    
    if os.path.exists(demo_check):
        print(f"\nTesting complete pipeline with: {demo_check}")
        
        try:
            # Test raw text extraction
            print("\n--- Step 1: OCR Text Extraction ---")
            raw_text = extractor.extract_raw_text(demo_check)
            print(f"Extracted {len(raw_text)} characters")
            print(f"Raw text preview:\n{raw_text[:200]}...")
            
            # Test LLM parsing
            print("\n--- Step 2: LLM Parsing ---")
            elements = extractor.parse_text_with_llm(raw_text)
            
            print(f"Parsing confidence: {elements.confidence_score:.2f}")
            print(f"Payee: {elements.payee}")
            print(f"Amount (digits): {elements.amount_digits}")
            print(f"Amount (words): {elements.amount_words}")
            print(f"Account number: {elements.account_number}")
            print(f"Date: {elements.date}")
            print(f"Check number: {elements.check_number}")
            print(f"Bank name: {elements.bank_name}")
            print(f"Signature present: {elements.signature_present}")
            
            # Test complete pipeline
            print("\n--- Step 3: Complete Pipeline ---")
            complete_result = extractor.extract_elements(demo_check)
            print(f"Complete pipeline confidence: {complete_result.confidence_score:.2f}")
            
        except Exception as e:
            print(f"❌ Pipeline test failed: {e}")
            import traceback
            traceback.print_exc()
    else:
        print(f"Demo check not found: {demo_check}")
        print("Run 'python create_demo_checks.py' first")

if __name__ == "__main__":
    test_ocr_llm_pipeline()
